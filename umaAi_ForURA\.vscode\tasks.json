{"version": "2.0.0", "tasks": [{"type": "cppbuild", "label": "C/C++: cl.exe build active file", "command": "cl.exe", "args": ["/Zi", "/EHsc", "/nologo", "/std:c++17", "/I${workspaceFolder}", "/I${workspaceFolder}/GameSimulator", "/Fe${fileDirname}\\${fileBasenameNoExtension}.exe", "${file}"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$msCompile"], "group": {"kind": "build", "isDefault": true}, "detail": "编译当前活动文件"}, {"label": "Build URA Project", "type": "shell", "command": "${workspaceFolder}/build.bat", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "detail": "构建整个URA项目"}, {"label": "Build and Run Current File", "type": "shell", "command": "cl.exe", "args": ["/EHsc", "/std:c++17", "/I${workspaceFolder}", "/I${workspaceFolder}/GameSimulator", "${file}", "&&", "${fileDirname}\\${fileBasenameNoExtension}.exe"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "detail": "编译并运行当前文件"}, {"label": "Clean Build", "type": "shell", "command": "${workspaceFolder}/cleanup_project.bat", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "detail": "清理构建文件"}]}