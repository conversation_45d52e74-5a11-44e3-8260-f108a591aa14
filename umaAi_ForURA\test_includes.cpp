#include <iostream>
#include <string>
#include <vector>
#include <unordered_map>

// 测试项目内部包含
// #include "GameConstants.cpp"  // 注释掉，因为这个文件结构有问题

int main() {
    std::cout << "=== 包含路径测试 ===" << std::endl;
    
    // 测试标准库
    std::string test = "标准库工作正常";
    std::vector<int> vec = {1, 2, 3};
    std::unordered_map<std::string, int> map;
    
    std::cout << test << std::endl;
    std::cout << "vector size: " << vec.size() << std::endl;
    std::cout << "map size: " << map.size() << std::endl;
    
    std::cout << "所有包含测试通过！" << std::endl;
    
    return 0;
}
