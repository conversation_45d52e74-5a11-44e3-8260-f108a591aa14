{"version": "0.2.0", "configurations": [{"name": "Debug Current File", "type": "cppvsdbg", "request": "launch", "program": "${fileDirname}\\${fileBasenameNoExtension}.exe", "args": [], "stopAtEntry": false, "cwd": "${fileDirname}", "environment": [], "console": "externalTerminal", "preLaunchTask": "C/C++: cl.exe build active file"}, {"name": "Debug Current File (Internal Console)", "type": "cppvsdbg", "request": "launch", "program": "${fileDirname}\\${fileBasenameNoExtension}.exe", "args": [], "stopAtEntry": false, "cwd": "${fileDirname}", "environment": [], "console": "integratedTerminal", "preLaunchTask": "C/C++: cl.exe build active file"}, {"name": "Debug URA Test Game", "type": "cppvsdbg", "request": "launch", "program": "${workspaceFolder}\\bin\\test_game.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}\\bin", "environment": [], "console": "externalTerminal", "preLaunchTask": "Build URA Project"}, {"name": "Debug URA Test Game (Internal Console)", "type": "cppvsdbg", "request": "launch", "program": "${workspaceFolder}\\bin\\test_game.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}\\bin", "environment": [], "console": "integratedTerminal", "preLaunchTask": "Build URA Project"}]}