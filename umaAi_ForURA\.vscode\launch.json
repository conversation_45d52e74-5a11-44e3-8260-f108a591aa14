{"version": "0.2.0", "configurations": [{"name": "Debug Current File", "type": "cppdbg", "request": "launch", "program": "${fileDirname}\\${fileBasenameNoExtension}.exe", "args": [], "stopAtEntry": false, "cwd": "${fileDirname}", "environment": [], "externalConsole": true, "MIMode": "gdb", "miDebuggerPath": "C:/msys64/ucrt64/bin/gdb.exe", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "C/C++: g++.exe build active file"}, {"name": "Debug Current File (Internal Console)", "type": "cppdbg", "request": "launch", "program": "${fileDirname}\\${fileBasenameNoExtension}.exe", "args": [], "stopAtEntry": false, "cwd": "${fileDirname}", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "C:/msys64/ucrt64/bin/gdb.exe", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "C/C++: g++.exe build active file"}, {"name": "Debug URA Test Game", "type": "cppvsdbg", "request": "launch", "program": "${workspaceFolder}\\bin\\test_game.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}\\bin", "environment": [], "console": "externalTerminal", "preLaunchTask": "Build URA Project"}, {"name": "Debug URA Test Game (Internal Console)", "type": "cppvsdbg", "request": "launch", "program": "${workspaceFolder}\\bin\\test_game.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}\\bin", "environment": [], "console": "integratedTerminal", "preLaunchTask": "Build URA Project"}]}