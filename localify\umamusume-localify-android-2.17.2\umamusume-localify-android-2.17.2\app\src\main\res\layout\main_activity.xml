<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".activity.MainActivity">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fitsSystemWindows="true"
            app:liftOnScroll="true">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/collapsing_layout"
                style="?collapsingToolbarLayoutLargeStyle"
                android:layout_width="match_parent"
                android:layout_height="?collapsingToolbarLayoutLargeSize"
                app:layout_scrollFlags="scroll|snap|exitUntilCollapsed">

                <TextView
                    style="@style/TextAppearance.Material3.HeadlineMedium"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:layout_marginStart="16dp"
                    android:layout_marginBottom="64dp"
                    android:text="@string/app_name"
                    app:layout_collapseMode="parallax" />

                <com.google.android.material.appbar.MaterialToolbar
                    android:id="@+id/app_bar"
                    android:layout_width="match_parent"
                    android:layout_height="?actionBarSize"
                    android:layout_gravity="bottom"
                    android:layout_marginStart="-8dp"
                    android:background="@android:color/transparent"
                    android:elevation="0dp"
                    app:layout_collapseMode="pin">

                    <Spinner
                        android:id="@+id/package_spinner"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:listitem="@layout/package_item" />

                </com.google.android.material.appbar.MaterialToolbar>
            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <FrameLayout
            android:id="@+id/frame"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <com.google.android.material.progressindicator.CircularProgressIndicator
                android:id="@+id/progress"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:indeterminate="true" />

            <LinearLayout
                android:id="@+id/perm_required"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    style="@style/TextAppearance.Material3.BodyLarge"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginBottom="16dp"
                    android:text="@string/prem_required_message_simple"
                    android:textAlignment="center" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/request_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:text="@android:string/ok" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/app_not_found"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:orientation="vertical"
                android:visibility="gone">

                <ImageView
                    android:layout_width="128dp"
                    android:layout_height="128dp"
                    android:layout_gravity="center"
                    android:contentDescription="@string/no_apps"
                    android:src="@drawable/ic_app_not_found" />

                <TextView
                    style="@style/TextAppearance.Material3.BodyLarge"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginBottom="16dp"
                    android:text="@string/no_apps"
                    android:textAlignment="center" />
            </LinearLayout>

        </FrameLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>
