#include <iostream>
#include <string>

int main() {
    std::cout << "=== VS Code C++ 配置测试 ===" << std::endl;
    std::cout << "编译器: MSVC" << std::endl;
    std::cout << "C++标准: C++17" << std::endl;
    std::cout << "项目: URA AI" << std::endl;
    
    // 测试C++17特性
    auto message = "配置成功！";
    std::cout << "测试结果: " << message << std::endl;
    
    std::cout << "\n按任意键退出..." << std::endl;
    std::cin.get();
    
    return 0;
}

