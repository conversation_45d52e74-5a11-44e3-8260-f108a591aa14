﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)messagepackanalyzer\3.1.3\build\MessagePackAnalyzer.targets" Condition="Exists('$(NuGetPackageRoot)messagepackanalyzer\3.1.3\build\MessagePackAnalyzer.targets')" />
  </ImportGroup>
</Project>