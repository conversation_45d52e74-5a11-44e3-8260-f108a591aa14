{"version": 3, "targets": {"net8.0": {"MessagePack/3.1.3": {"type": "package", "dependencies": {"MessagePack.Annotations": "3.1.3", "MessagePackAnalyzer": "3.1.3", "Microsoft.NET.StringTools": "17.11.4"}, "compile": {"lib/net8.0/MessagePack.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/MessagePack.dll": {"related": ".xml"}}}, "MessagePack.Annotations/3.1.3": {"type": "package", "compile": {"lib/netstandard2.0/MessagePack.Annotations.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/MessagePack.Annotations.dll": {"related": ".xml"}}}, "MessagePackAnalyzer/3.1.3": {"type": "package", "build": {"build/MessagePackAnalyzer.targets": {}}}, "Microsoft.NET.StringTools/17.11.4": {"type": "package", "compile": {"ref/net8.0/_._": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.NET.StringTools.dll": {"related": ".pdb;.xml"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "Spectre.Console/0.50.0": {"type": "package", "dependencies": {"System.Memory": "4.6.3"}, "compile": {"lib/net8.0/Spectre.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Spectre.Console.dll": {"related": ".xml"}}}, "System.Memory/4.6.3": {"type": "package", "compile": {"lib/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}}}, "libraries": {"MessagePack/3.1.3": {"sha512": "UiNv3fknvPzh5W+S0VV96R17RBZQQU71qgmsMnjjRZU2rtQM/XcTnOB+klT2dA6T1mxjnNKYrEm164AoXvGmYg==", "type": "package", "path": "messagepack/3.1.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net472/MessagePack.dll", "lib/net472/MessagePack.xml", "lib/net8.0/MessagePack.dll", "lib/net8.0/MessagePack.xml", "lib/net9.0/MessagePack.dll", "lib/net9.0/MessagePack.xml", "lib/netstandard2.0/MessagePack.dll", "lib/netstandard2.0/MessagePack.xml", "lib/netstandard2.1/MessagePack.dll", "lib/netstandard2.1/MessagePack.xml", "messagepack.3.1.3.nupkg.sha512", "messagepack.nuspec"]}, "MessagePack.Annotations/3.1.3": {"sha512": "XTy4njgTAf6UVBKFj7c7ad5R0WVKbvAgkbYZy4f00kplzX2T3VOQ34AUke/Vn/QgQZ7ETdd34/IDWS3KBInSGA==", "type": "package", "path": "messagepack.annotations/3.1.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/MessagePack.Annotations.dll", "lib/netstandard2.0/MessagePack.Annotations.xml", "messagepack.annotations.3.1.3.nupkg.sha512", "messagepack.annotations.nuspec"]}, "MessagePackAnalyzer/3.1.3": {"sha512": "19u1oVNv2brCs5F/jma8O8CnsKMMpYwNqD0CAEDEzvqwDTAhqC9r7xHZP4stPb3APs/ryO/zVn7LvjoEHfvs7Q==", "type": "package", "path": "messagepackanalyzer/3.1.3", "files": [".nupkg.metadata", ".signature.p7s", "analyzers/roslyn4.3/cs/MessagePack.Analyzers.CodeFixes.dll", "analyzers/roslyn4.3/cs/MessagePack.SourceGenerator.dll", "build/MessagePackAnalyzer.targets", "messagepackanalyzer.3.1.3.nupkg.sha512", "messagepackanalyzer.nuspec"]}, "Microsoft.NET.StringTools/17.11.4": {"sha512": "mudqUHhNpeqIdJoUx2YDWZO/I9uEDYVowan89R6wsomfnUJQk6HteoQTlNjZDixhT2B4IXMkMtgZtoceIjLRmA==", "type": "package", "path": "microsoft.net.stringtools/17.11.4", "files": [".nupkg.metadata", ".signature.p7s", "MSBuild-NuGet-Icon.png", "README.md", "lib/net472/Microsoft.NET.StringTools.dll", "lib/net472/Microsoft.NET.StringTools.pdb", "lib/net472/Microsoft.NET.StringTools.xml", "lib/net8.0/Microsoft.NET.StringTools.dll", "lib/net8.0/Microsoft.NET.StringTools.pdb", "lib/net8.0/Microsoft.NET.StringTools.xml", "lib/netstandard2.0/Microsoft.NET.StringTools.dll", "lib/netstandard2.0/Microsoft.NET.StringTools.pdb", "lib/netstandard2.0/Microsoft.NET.StringTools.xml", "microsoft.net.stringtools.17.11.4.nupkg.sha512", "microsoft.net.stringtools.nuspec", "notices/THIRDPARTYNOTICES.txt", "ref/net472/Microsoft.NET.StringTools.dll", "ref/net472/Microsoft.NET.StringTools.xml", "ref/net8.0/Microsoft.NET.StringTools.dll", "ref/net8.0/Microsoft.NET.StringTools.xml", "ref/netstandard2.0/Microsoft.NET.StringTools.dll", "ref/netstandard2.0/Microsoft.NET.StringTools.xml"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "Spectre.Console/0.50.0": {"sha512": "gkwYncFqMjlPF6Yz8KeVsKWpdfv15sr6uKm+vXVa/8Q4tvx52LPzMQOO5m+bXwI3AGGFeY3cok3ZJbguYizswg==", "type": "package", "path": "spectre.console/0.50.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net8.0/Spectre.Console.dll", "lib/net8.0/Spectre.Console.xml", "lib/net9.0/Spectre.Console.dll", "lib/net9.0/Spectre.Console.xml", "lib/netstandard2.0/Spectre.Console.dll", "lib/netstandard2.0/Spectre.Console.xml", "logo.png", "spectre.console.0.50.0.nupkg.sha512", "spectre.console.nuspec"]}, "System.Memory/4.6.3": {"sha512": "qdcDOgnFZY40+Q9876JUHnlHu7bosOHX8XISRoH94fwk6hgaeQGSgfZd8srWRZNt5bV9ZW2TljcegDNxsf+96A==", "type": "package", "path": "system.memory/4.6.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net461/System.Memory.targets", "buildTransitive/net462/_._", "lib/net462/System.Memory.dll", "lib/net462/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "lib/netstandard2.1/_._", "system.memory.4.6.3.nupkg.sha512", "system.memory.nuspec"]}}, "projectFileDependencyGroups": {"net8.0": ["MessagePack >= 3.1.3", "Newtonsoft.Json >= 13.0.3", "Spectre.Console >= 0.50.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "D:\\Visual Studio\\subassembly_and_SDK\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\URA源代码\\firstplugins\\firstplugins.csproj", "projectName": "MyFirstPlugin", "projectPath": "C:\\Users\\<USER>\\Desktop\\URA源代码\\firstplugins\\firstplugins.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\URA源代码\\firstplugins\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Visual Studio\\subassembly_and_SDK\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"MessagePack": {"target": "Package", "version": "[3.1.3, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Spectre.Console": {"target": "Package", "version": "[0.50.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}