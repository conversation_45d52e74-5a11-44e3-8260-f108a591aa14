﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AssemblyName>MyFirstPlugin</AssemblyName>
    <OutputPath>bin\</OutputPath>
    <UseWPF>false</UseWPF>
    <UseWindowsForms>false</UseWindowsForms>
  </PropertyGroup>

  <ItemGroup>
    <!-- URA程序集引用 - 使用DLL而不是EXE -->
    <Reference Include="UmamusumeResponseAnalyzer">
      <HintPath>C:\Users\<USER>\Desktop\URA源代码\URA1.14.1.3\UmamusumeResponseAnalyzer-1.14.1.3\UmamusumeResponseAnalyzer\bin\Debug\net8.0\UmamusumeResponseAnalyzer.dll</HintPath>
      <Private>false</Private>
    </Reference>

    <!-- 必要的NuGet包 - 版本与URA保持一致 -->
    <PackageReference Include="MessagePack" Version="3.1.3" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Spectre.Console" Version="0.50.0" />
  </ItemGroup>
</Project>