# MessagePack转JSON插件

## 📚 插件简介

这是一个URA插件，用于将接收到的MessagePack数据自动转换为JSON格式并保存到指定路径。

## 🎯 主要功能

1. **自动转换**: 将MessagePack数据转换为格式化的JSON文件
2. **自定义路径**: 可以自定义JSON文件的保存路径
3. **时间戳命名**: JSON文件按照日期时间自动命名 (格式: `MMddHHmmss`)
4. **分类保存**: 分别保存请求数据和响应数据
5. **实时配置**: 支持运行时开启/关闭功能

## ⚙️ 配置选项

### 基础设置
- **是否启用插件功能**: 总开关，控制插件是否工作
- **是否输出MessagePack转换的JSON文件**: 控制是否保存JSON文件

### 高级设置
- **JSON文件保存路径**: 自定义保存路径 (留空使用默认路径)
- **是否同时保存请求数据**: 控制是否保存请求数据
- **是否同时保存响应数据**: 控制是否保存响应数据

### 默认保存路径
```
桌面/URA_MessagePack_JSON/
```

## 📁 文件命名规则

### 文件名格式
```
{类型}_{月日时分秒}.json
```

### 示例
```
request_1225143052.json   // 请求数据 - 12月25日14点30分52秒
response_1225143053.json  // 响应数据 - 12月25日14点30分53秒
```

## 🚀 使用方法

### 1. 编译插件
```bash
# 在插件目录下执行
dotnet build
```

### 2. 复制到URA插件目录
```bash
# 将编译后的DLL文件复制到URA的plugins目录
cp bin/Debug/net8.0/firstplugins.dll /path/to/URA/plugins/
```

### 3. 启动URA
- 启动URA程序
- 在插件设置中找到 "MessagePack转JSON插件"
- 根据需要调整配置选项

### 4. 开始游戏
- 启动游戏并进行操作
- 插件会自动拦截MessagePack数据并转换保存

## 📊 输出示例

### 保存的JSON文件结构
```json
{
  "data": {
    "viewer_id": 123456789,
    "single_mode_chara": {
      "chara_id": 1001,
      "speed": 850,
      "stamina": 720,
      "power": 680,
      "guts": 590,
      "wisdom": 750
    }
  },
  "timestamp": 1640419200
}
```

### 控制台输出
```
✓ JSON文件已保存: C:\Users\<USER>\Desktop\URA_MessagePack_JSON\response_1225143052.json
✓ JSON文件已保存: C:\Users\<USER>\Desktop\URA_MessagePack_JSON\request_1225143053.json
```

## 🔧 高级配置

### 自定义保存路径示例
```
D:\GameData\URA_JSON\
C:\Users\<USER>\Documents\URA_Data\
```

### 配置文件位置
插件设置会自动保存在URA的配置文件中，无需手动配置。

## ⚠️ 注意事项

1. **磁盘空间**: 长时间运行可能产生大量JSON文件，注意磁盘空间
2. **性能影响**: 大量数据转换可能影响性能，可根据需要关闭不必要的功能
3. **路径权限**: 确保指定的保存路径有写入权限
4. **文件覆盖**: 相同时间戳的文件会被覆盖 (概率极低)

## 🐛 故障排除

### 常见问题

#### 1. 插件无法加载
- 检查DLL文件是否在正确的plugins目录
- 确认.NET版本兼容性
- 查看URA控制台的错误信息

#### 2. 文件保存失败
- 检查保存路径是否存在写入权限
- 确认磁盘空间是否充足
- 查看控制台的错误信息

#### 3. 没有生成JSON文件
- 确认插件已启用
- 检查相关的保存选项是否开启
- 确认游戏有网络数据交互

### 调试信息
插件会在URA控制台输出详细的运行信息，包括：
- 初始化状态
- 文件保存结果
- 错误信息

## 📝 更新日志

### v1.0.0
- 初始版本
- 支持MessagePack到JSON转换
- 支持自定义保存路径
- 支持时间戳文件命名
- 支持请求/响应数据分类保存

## 👨‍💻 开发者信息

- **作者**: muxueliuNian
- **版本**: 1.0.0
- **兼容**: URA 最新版本

## 📄 许可证

本插件遵循与URA相同的许可证协议。
