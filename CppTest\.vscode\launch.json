{"version": "0.2.0", "configurations": [{"name": "C/C++: cl.exe 调试", "type": "cppvsdbg", "request": "launch", "program": "${fileDirname}\\${fileBasenameNoExtension}.exe", "args": [], "stopAtEntry": false, "cwd": "${fileDirname}", "environment": [], "console": "externalTerminal", "preLaunchTask": "C/C++: cl.exe 生成活动文件"}, {"name": "C/C++: cl.exe 内部终端调试", "type": "cppvsdbg", "request": "launch", "program": "${fileDirname}\\${fileBasenameNoExtension}.exe", "args": [], "stopAtEntry": false, "cwd": "${fileDirname}", "environment": [], "console": "integratedTerminal", "preLaunchTask": "C/C++: cl.exe 生成活动文件"}]}