{"files.associations": {"*.cpp": "cpp", "*.h": "c", "*.hpp": "cpp"}, "files.encoding": "utf8", "files.autoGuessEncoding": true, "C_Cpp.default.cppStandard": "c++17", "C_Cpp.default.cStandard": "c17", "C_Cpp.default.intelliSenseMode": "windows-msvc-x64", "code-runner.executorMap": {"cpp": "cd $dir && cl /EHsc /nologo /std:c++17 $fileName /Fe$fileNameWithoutExt.exe && $fileNameWithoutExt.exe"}, "code-runner.runInTerminal": true, "code-runner.saveFileBeforeRun": true, "code-runner.clearPreviousOutput": true}