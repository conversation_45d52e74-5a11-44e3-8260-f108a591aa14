<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="pref_module_version">Module version</string>

    <!-- Logger prefs -->
    <string name="pref_cate_logger">Logger</string>
    <string name="pref_enable_logger">Enable logger</string>
    <string name="pref_dump_static_entries">Dump static entries</string>
    <string name="pref_dump_db_entries">Dump database entries</string>
    <string name="pref_dump_msgpack">Dump MessagePack</string>

    <!-- Dump MessagePack prefs -->
    <string name="pref_msgpack_dump_request">Dump request</string>
    <string name="pref_msgpack_notifier_host">Notifier host</string>
    <string name="pref_msgpack_history">MessagePack history</string>

    <!-- Graphics prefs -->
    <string name="pref_cate_graphics">Graphics</string>
    <string name="pref_max_fps">Max FPS</string>
    <string name="pref_ui_animation_scale">UI animation scale</string>
    <string name="pref_3d_resolution_scale">3D rendering resolution scale</string>
    <string name="pref_graphics_quality">Graphics quality</string>
    <string name="pref_anti_aliasing">Anti-aliasing</string>
    <string name="pref_cyspring_update_mode">CySpring update mode</string>

    <!-- Screen prefs -->
    <string name="pref_cate_screen">Screen</string>
    <string name="pref_use_system_resolution">Use system resolution</string>
    <string name="pref_replace_builtin_font">Replace to builtin font</string>
    <string name="pref_replace_custom_font">Replace to custom font</string>
    <string name="pref_font_asset_path">Font asset bundle</string>
    <string name="pref_font_asset_name">Font asset name</string>
    <string name="pref_tmpro_font_asset_name">TextMeshPro font asset name</string>
    <string name="pref_force_landscape">Force landscape</string>
    <string name="pref_force_landscape_ui_scale">UI scale on force landscape</string>
    <string name="pref_character_system_text_caption">Character dialogue caption</string>

    <!-- ETC prefs -->
    <string name="pref_cate_etc">ETC</string>

    <string name="pref_replace_assets_path">The folder containing the asset to be replaced</string>
    <string name="pref_replace_asset_bundle">Asset bundle for replacement</string>
    <string name="pref_loading_show_orientation_guide">Show screen rotation guide while loading</string>
    <string name="pref_hide_loading_screen">Hide loading screen</string>

    <string name="pref_restore_notification">Restore notification</string>

    <string name="pref_restore_gallop_webview">Restore in-app webview</string>
    <string name="pref_restore_gallop_webview_summary">Show in-app WebView instead of 3rd-party WebView</string>

    <string name="pref_use_third_party_news">Use 3rd-party news</string>
    <string name="pref_use_third_party_news_summary">Use 3rd-party news instead of in-app news</string>

    <string name="pref_static_entries_use_hash">Using hash for static entries</string>
    <string name="pref_static_entries_use_text_id">Using TextId for static entries</string>
    <string name="pref_text_id_file">TextId file</string>
</resources>
