﻿using Newtonsoft.Json.Linq;
using UmamusumeResponseAnalyzer.Plugin;
using Spectre.Console;
using System;
using System.Threading.Tasks;
using System.IO;
using MessagePack;
using Newtonsoft.Json;

namespace firstplugins
{
    public class GetClubFansPlugin : IPlugin
    {
        public string Name => "GetClubFans";
        public string Author => "muxueliuNian";
        public Version Version => new Version(1, 0, 1);
        public string[] Targets => ["Cygames", "Komoe"];

        [PluginSetting]
        [PluginDescription("是否启用插件功能")]
        public bool EnablePlugin { get; set; } = true;

        [PluginSetting]
        [PluginDescription("是否输出MessagePack转换的JSON文件")]
        public bool IsOutputJsonFiles { get; set; } = true;

        [PluginSetting]
        [PluginDescription("JSON文件保存路径 (留空使用默认路径)")]
        public string CustomSavePath { get; set; } = "";

        [PluginSetting]
        [PluginDescription("是否同时保存请求数据")]
        public bool SaveRequestData { get; set; } = true;

        [PluginSetting]
        [PluginDescription("是否同时保存响应数据")]
        public bool SaveResponseData { get; set; } = true;

        [PluginSetting]
        [PluginDescription("是否启用社团粉丝数记录功能")]
        public bool EnableClubFansTracking { get; set; } = true;

        [PluginSetting]
        [PluginDescription("社团粉丝数据保存路径 (留空使用默认路径)")]
        public string ClubFansDataPath { get; set; } = "";

        // 默认保存路径
        private string DefaultSavePath => Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "URA_MessagePack_JSON");

        // 默认社团粉丝数据保存路径
        private string DefaultClubFansPath => Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "URA_ClubFans_Data");

        // 获取实际使用的保存路径
        private string GetSavePath()
        {
            string savePath = string.IsNullOrWhiteSpace(CustomSavePath) ? DefaultSavePath : CustomSavePath;

            // 确保目录存在
            if (!Directory.Exists(savePath))
            {
                Directory.CreateDirectory(savePath);
            }

            return savePath;
        }

        // 获取社团粉丝数据保存路径
        private string GetClubFansPath()
        {
            string savePath = string.IsNullOrWhiteSpace(ClubFansDataPath) ? DefaultClubFansPath : ClubFansDataPath;

            // 确保目录存在
            if (!Directory.Exists(savePath))
            {
                Directory.CreateDirectory(savePath);
            }

            return savePath;
        }

        // 生成基于日期时间的文件名
        private string GenerateFileName(string prefix = "data")
        {
            string timestamp = DateTime.Now.ToString("MMddHHmmss");
            return $"{prefix}_{timestamp}.json";
        }

        // 将MessagePack数据转换为JSON并保存
        private async Task SaveMessagePackAsJson(byte[] messagePackData, string filePrefix)
        {
            try
            {
                if (!EnablePlugin || !IsOutputJsonFiles || messagePackData == null || messagePackData.Length == 0)
                    return;

                // 转换MessagePack到JSON
                var jsonObject = MessagePackSerializer.ConvertToJson(messagePackData);

                // 格式化JSON (美化输出)
                var parsedJson = JToken.Parse(jsonObject);
                string formattedJson = parsedJson.ToString(Formatting.Indented);

                // 生成文件路径
                string savePath = GetSavePath();
                string fileName = GenerateFileName(filePrefix);
                string fullPath = Path.Combine(savePath, fileName);

                // 异步保存文件
                await File.WriteAllTextAsync(fullPath, formattedJson);

                // 输出成功信息
                AnsiConsole.MarkupLine($"[green]✓[/] JSON文件已保存: [blue]{fullPath}[/]");
            }
            catch (Exception ex)
            {
                AnsiConsole.MarkupLine($"[red]✗[/] 保存JSON文件失败: [red]{ex.Message}[/]");
            }
        }

        // 检测并处理社团粉丝数据
        private async Task ProcessClubFansData(JObject data)
        {
            try
            {
                if (!EnablePlugin || !EnableClubFansTracking)
                    return;

                // 检查是否包含社团信息
                if (data["circle_info"] is JObject circleInfo &&
                    data["summary_user_info_array"] is JArray userInfoArray)
                {
                    // 提取社团名称
                    string clubName = circleInfo["name"]?.ToString();
                    if (string.IsNullOrEmpty(clubName))
                    {
                        AnsiConsole.MarkupLine("[yellow]⚠[/] 社团名称为空，跳过处理");
                        return;
                    }

                    AnsiConsole.MarkupLine($"[green]✓[/] 已检测到社团: [cyan]{clubName}[/] 的信息");

                    // 构建社团成员粉丝数据
                    var clubData = new JObject();
                    var membersData = new JObject();

                    foreach (JObject userInfo in userInfoArray)
                    {
                        string memberId = userInfo["name"]?.ToString();
                        int fanCount = userInfo["fan"]?.Value<int>() ?? 0;

                        if (!string.IsNullOrEmpty(memberId))
                        {
                            membersData[memberId] = fanCount;
                            AnsiConsole.MarkupLine($"[blue]📊[/] 成员 [yellow]{memberId}[/]: [green]{fanCount}[/] 粉丝");
                        }
                    }

                    clubData[clubName] = membersData;

                    // 保存社团粉丝数据
                    await SaveClubFansData(clubData, clubName);
                }
                else
                {
                    AnsiConsole.MarkupLine("[gray]ℹ[/] 未监测到社团messagepack信息");
                }
            }
            catch (Exception ex)
            {
                AnsiConsole.MarkupLine($"[red]✗[/] 处理社团粉丝数据失败: [red]{ex.Message}[/]");
            }
        }

        // 保存社团粉丝数据到JSON文件
        private async Task SaveClubFansData(JObject clubData, string clubName)
        {
            try
            {
                string savePath = GetClubFansPath();
                string dateString = DateTime.Now.ToString("yyyyMMdd");
                string fileName = $"{dateString}.json";
                string fullPath = Path.Combine(savePath, fileName);

                // 检查文件是否已存在，如果存在则合并数据
                JObject existingData = new JObject();
                if (File.Exists(fullPath))
                {
                    try
                    {
                        string existingContent = await File.ReadAllTextAsync(fullPath);
                        existingData = JObject.Parse(existingContent);
                    }
                    catch (Exception ex)
                    {
                        AnsiConsole.MarkupLine($"[yellow]⚠[/] 读取现有文件失败，将创建新文件: {ex.Message}");
                        existingData = new JObject();
                    }
                }

                // 合并新数据到现有数据中
                foreach (var property in clubData.Properties())
                {
                    existingData[property.Name] = property.Value;
                }

                // 格式化JSON输出
                string formattedJson = existingData.ToString(Formatting.Indented);

                // 异步保存文件
                await File.WriteAllTextAsync(fullPath, formattedJson);

                AnsiConsole.MarkupLine($"[green]✓[/] 社团粉丝数据已保存: [blue]{fullPath}[/]");
            }
            catch (Exception ex)
            {
                AnsiConsole.MarkupLine($"[red]✗[/] 保存社团粉丝数据失败: [red]{ex.Message}[/]");
            }
        }

        [Analyzer(response: false, priority: 0)]
        public async void AnalyzeRequest(JObject request)
        {
            if (!EnablePlugin || !SaveRequestData)
                return;

            try
            {
                // 从JObject重新序列化为MessagePack格式
                string jsonString = request.ToString(Formatting.None);
                byte[] messagePackData = MessagePackSerializer.ConvertFromJson(jsonString);

                await SaveMessagePackAsJson(messagePackData, "request");
            }
            catch (Exception ex)
            {
                AnsiConsole.MarkupLine($"[red]✗[/] 处理请求数据失败: [red]{ex.Message}[/]");
            }
        }

        [Analyzer(response: true, priority: 0)]
        public async void AnalyzeResponse(JObject response)
        {
            if (!EnablePlugin)
                return;

            try
            {
                // 处理社团粉丝数据 (优先级最高)
                if (response["data"] is JObject data)
                {
                    await ProcessClubFansData(data);
                }

                // 保存MessagePack转JSON文件 (如果启用)
                if (SaveResponseData)
                {
                    // 从JObject重新序列化为MessagePack格式
                    string jsonString = response.ToString(Formatting.None);
                    byte[] messagePackData = MessagePackSerializer.ConvertFromJson(jsonString);

                    await SaveMessagePackAsJson(messagePackData, "response");
                }
            }
            catch (Exception ex)
            {
                AnsiConsole.MarkupLine($"[red]✗[/] 处理响应数据失败: [red]{ex.Message}[/]");
            }
        }

        public void Initialize()
        {
            AnsiConsole.MarkupLine($"[green]✓[/] {Name} v{Version} 已初始化");

            if (!EnablePlugin)
            {
                AnsiConsole.MarkupLine($"[red]⏸[/] 插件功能已禁用 - 所有功能将不会运行");
                return;
            }

            AnsiConsole.MarkupLine($"[blue]ℹ[/] JSON保存路径: [yellow]{GetSavePath()}[/]");

            if (EnableClubFansTracking)
            {
                AnsiConsole.MarkupLine($"[blue]ℹ[/] 社团粉丝数据保存路径: [yellow]{GetClubFansPath()}[/]");
                AnsiConsole.MarkupLine($"[green]🎯[/] 社团粉丝数记录功能已启用");
            }
            else
            {
                AnsiConsole.MarkupLine($"[gray]⏸[/] 社团粉丝数记录功能已禁用");
            }

            AnsiConsole.MarkupLine($"[green]🚀[/] 插件已准备就绪，开始监听游戏数据");
        }

        public void Dispose()
        {
            AnsiConsole.MarkupLine($"[yellow]![/] {Name} 已卸载");
        }

        public Task UpdatePlugin(ProgressContext _)
        {
            return Task.CompletedTask;
        }
    }
}

