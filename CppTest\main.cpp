#include <iostream>
#include <string>
#include <vector>
#include <chrono>
#include <thread>
#include <memory>

using namespace std;

// 测试类
class TestClass {
private:
    string name;
    int value;

public:
    TestClass(const string& n, int v) : name(n), value(v) {}
    
    void display() const {
        cout << "Name: " << name << ", Value: " << value << endl;
    }
    
    int getValue() const { return value; }
};

// 测试函数
void testBasicFeatures() {
    cout << "\n=== 基本功能测试 ===" << endl;
    
    // 测试变量和输入输出
    string userName;
    cout << "请输入您的名字: ";
    getline(cin, userName);
    cout << "你好, " << userName << "!" << endl;
    
    // 测试STL容器
    vector<int> numbers = {1, 2, 3, 4, 5};
    cout << "测试vector: ";
    for (int num : numbers) {
        cout << num << " ";
    }
    cout << endl;
    
    // 测试类
    TestClass obj("测试对象", 42);
    obj.display();
}

void testAdvancedFeatures() {
    cout << "\n=== 高级功能测试 ===" << endl;
    
    // 测试Lambda表达式
    auto lambda = [](int x, int y) -> int {
        return x + y;
    };
    cout << "Lambda测试: 3 + 4 = " << lambda(3, 4) << endl;
    
    // 测试智能指针
    auto ptr = make_unique<TestClass>("智能指针测试", 100);
    ptr->display();
    
    // 测试范围for循环
    vector<string> words = {"Hello", "World", "C++", "Test"};
    cout << "范围for循环测试: ";
    for (const auto& word : words) {
        cout << word << " ";
    }
    cout << endl;
    
    // 测试多线程
    cout << "多线程测试: ";
    thread t1([]() {
        this_thread::sleep_for(chrono::milliseconds(100));
        cout << "线程1 ";
    });
    
    thread t2([]() {
        this_thread::sleep_for(chrono::milliseconds(50));
        cout << "线程2 ";
    });
    
    t1.join();
    t2.join();
    cout << "完成" << endl;
}

void testCompilerInfo() {
    cout << "\n=== 编译器信息 ===" << endl;
    
#ifdef _MSC_VER
    cout << "编译器: Microsoft Visual C++ " << _MSC_VER << endl;
#elif defined(__GNUC__)
    cout << "编译器: GCC " << __GNUC__ << "." << __GNUC_MINOR__ << endl;
#elif defined(__clang__)
    cout << "编译器: Clang " << __clang_major__ << "." << __clang_minor__ << endl;
#endif

#ifdef _WIN32
    cout << "平台: Windows" << endl;
#elif defined(__linux__)
    cout << "平台: Linux" << endl;
#elif defined(__APPLE__)
    cout << "平台: macOS" << endl;
#endif

    cout << "C++标准: " << __cplusplus << endl;
    
#ifdef _DEBUG
    cout << "构建模式: Debug" << endl;
#else
    cout << "构建模式: Release" << endl;
#endif
}

int main()
{
    cout << "=== C++ 环境配置测试程序 ===" << endl;
    cout << "测试Visual Studio C++编译器配置" << endl;
    
    try {
        testCompilerInfo();
        testBasicFeatures();
        testAdvancedFeatures();
        
        cout << "\n=== 测试完成 ===" << endl;
        cout << "所有测试通过！C++环境配置正确。" << endl;
        
    } catch (const exception& e) {
        cout << "错误: " << e.what() << endl;
        return 1;
    }
    
    cout << "\n按任意键退出...";
    cin.get();
    return 0;
}
