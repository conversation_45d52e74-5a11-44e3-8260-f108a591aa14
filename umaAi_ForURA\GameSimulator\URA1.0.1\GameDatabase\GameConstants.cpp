#pragma once
#include <string>
#include <vector>
#include <unordered_map>

const int TOTAL_TURN = 78; //游戏总回合
const int MAX_HEAD_NUM = 8; //最多9个头

class GameConstants
{
    public:
        static const int TrainingBasicValue[5][5][7];
        //TrainingBasicValue[第几种训练][LV几][速耐力根智pt体力]
        //static const int FailRateBasic; 暂时还没理解是什么意思
        static const int BasicFiveStatusLimit[5];//初始上限，1200以上翻倍
        
}
