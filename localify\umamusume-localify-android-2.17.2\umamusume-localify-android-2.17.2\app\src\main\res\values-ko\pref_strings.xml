<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="pref_module_version">모듈 버전</string>

    <string name="pref_cate_logger">로거</string>
    <string name="pref_enable_logger">로거 활성화</string>
    <string name="pref_dump_static_entries">정적 항목 덤프</string>
    <string name="pref_dump_db_entries">DB 항목 덤프</string>
    <string name="pref_dump_msgpack">MessagePack 덤프</string>

    <string name="pref_msgpack_dump_request">요청 덤프</string>
    <string name="pref_msgpack_notifier_host">Notifier 호스트</string>
    <string name="pref_msgpack_history">MessagePack 기록</string>

    <string name="pref_cate_graphics">그래픽</string>
    <string name="pref_max_fps">최대 FPS</string>
    <string name="pref_ui_animation_scale">UI 애니메이션 배율</string>
    <string name="pref_3d_resolution_scale">3D 렌더링 해상도 배율</string>
    <string name="pref_graphics_quality">그래픽 품질</string>
    <string name="pref_anti_aliasing">안티 엘리어싱</string>
    <string name="pref_cyspring_update_mode">CySpring 업데이트 모드</string>

    <string name="pref_cate_screen">화면</string>
    <string name="pref_use_system_resolution">시스템 해상도 사용</string>
    <string name="pref_replace_builtin_font">내장된 폰트로 교체</string>
    <string name="pref_replace_custom_font">사용자 지정 폰트로 교체</string>
    <string name="pref_font_asset_path">폰트 에셋 번들</string>
    <string name="pref_font_asset_name">폰트 에셋 이름</string>
    <string name="pref_tmpro_font_asset_name">TextMeshPro 폰트 에셋 이름</string>
    <string name="pref_force_landscape">강제 가로화면</string>
    <string name="pref_force_landscape_ui_scale">강제 가로화면에서의 UI 배율</string>
    <string name="pref_character_system_text_caption">캐릭터 대사 자막</string>

    <string name="pref_cate_etc">기타</string>
    <string name="pref_replace_assets_path">교체할 에셋이 들어있는 폴더</string>
    <string name="pref_replace_asset_bundle">교체용 에셋 번들</string>
    <string name="pref_loading_show_orientation_guide">로딩 중 화면 회전 가이드 표시</string>
    <string name="pref_hide_loading_screen">로딩 화면 숨기기</string>

    <string name="pref_restore_notification">알림 복원</string>

    <string name="pref_restore_gallop_webview">인앱 WebView 복원</string>
    <string name="pref_restore_gallop_webview_summary">타사 WebView 대신 인앱 WebView를 표시합니다</string>

    <string name="pref_use_third_party_news">타사 공지 사용</string>
    <string name="pref_use_third_party_news_summary">인앱 공지 대신 타사 공지를 사용합니다</string>

    <string name="pref_static_entries_use_hash">정적 항목 로드 시 해시 사용</string>
    <string name="pref_static_entries_use_text_id">정적 항목 로드 시 TextId 사용</string>
    <string name="pref_text_id_file">TextId 파일</string>
</resources>
