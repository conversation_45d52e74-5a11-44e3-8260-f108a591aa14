# C++游戏AI开发教学文档

## 🎯 学习目标

本文档专门针对UmaAI项目开发需求，帮助您快速掌握项目中用到的C++语法和概念。

## 📋 目录

1. [基础语法](#基础语法)
2. [结构体和类](#结构体和类)
3. [容器和数组](#容器和数组)
4. [指针和引用](#指针和引用)
5. [函数和方法](#函数和方法)
6. [枚举和常量](#枚举和常量)
7. [文件操作](#文件操作)
8. [随机数生成](#随机数生成)
9. [项目实战技巧](#项目实战技巧)

---

## 1. 基础语法

### 1.1 变量声明和初始化

```cpp
// 基本数据类型
int turn = 0;           // 整数
double score = 1250.5;  // 浮点数
bool isWin = true;      // 布尔值
char grade = 'A';       // 字符

// 字符串
std::string name = "ウマ娘";  // 需要 #include <string>

// 数组
int attributes[5] = {100, 100, 100, 100, 100};  // 固定大小数组
int status[5] = {0};     // 全部初始化为0
```

### 1.2 条件语句

```cpp
// if-else 语句
if (vital < 30) {
    // 体力低时休息
    action = REST;
} else if (vital > 80) {
    // 体力充足时训练
    action = TRAIN_SPEED;
} else {
    // 其他情况
    action = OUTGOING;
}

// switch 语句
switch (action_type) {
    case TRAIN_SPEED:
        processTraining(0);
        break;
    case TRAIN_STAMINA:
        processTraining(1);
        break;
    case REST:
        processRest();
        break;
    default:
        // 默认情况
        break;
}
```

### 1.3 循环语句

```cpp
// for 循环
for (int i = 0; i < 5; i++) {
    status[i] += 10;  // 给每个属性加10
}

// 范围for循环 (C++11)
std::vector<int> skills = {1, 2, 3, 4, 5};
for (int skill : skills) {
    std::cout << "技能ID: " << skill << std::endl;
}

// while 循环
int turn = 0;
while (turn < 78 && !isGameEnd()) {
    processOneTurn();
    turn++;
}
```

---

## 2. 结构体和类

### 2.1 结构体 (struct)

```cpp
// 游戏状态结构体
struct URAGame {
    // 成员变量
    int turn;
    int vital;
    int speed, stamina, power, guts, wisdom;
    
    // 构造函数
    URAGame() {
        turn = 0;
        vital = 100;
        speed = stamina = power = guts = wisdom = 100;
    }
    
    // 成员函数
    int getTotalStatus() const {
        return speed + stamina + power + guts + wisdom;
    }
    
    bool isGameEnd() const {
        return turn >= 78;
    }
};

// 使用结构体
URAGame game;
game.turn = 10;
game.vital = 80;
int total = game.getTotalStatus();
```

### 2.2 类 (class)

```cpp
class GameSimulator {
private:  // 私有成员，外部无法访问
    URAGame game_state;
    std::mt19937 rng;
    
public:   // 公有成员，外部可以访问
    // 构造函数
    GameSimulator() : rng(std::time(nullptr)) {
        // 初始化随机数生成器
    }
    
    // 析构函数
    ~GameSimulator() {
        // 清理资源
    }
    
    // 公有方法
    void startNewGame() {
        game_state = URAGame();
    }
    
    bool executeAction(const Action& action) {
        // 执行动作的逻辑
        return true;
    }
    
    // const方法 - 不修改对象状态
    const URAGame& getGameState() const {
        return game_state;
    }
};
```

---

## 3. 容器和数组

### 3.1 std::vector (动态数组)

```cpp
#include <vector>

// 声明和初始化
std::vector<int> skills;                    // 空向量
std::vector<int> attributes = {100, 100, 100, 100, 100};  // 初始化列表
std::vector<std::string> names(10);        // 10个空字符串

// 常用操作
skills.push_back(123);          // 添加元素
skills.push_back(456);
int size = skills.size();       // 获取大小
bool empty = skills.empty();    // 检查是否为空

// 访问元素
int first_skill = skills[0];    // 下标访问
int last_skill = skills.back(); // 最后一个元素

// 遍历
for (size_t i = 0; i < skills.size(); i++) {
    std::cout << skills[i] << " ";
}

// 范围for循环
for (int skill : skills) {
    std::cout << skill << " ";
}
```

### 3.2 固定数组

```cpp
// 声明固定大小数组
int status[5];                  // 未初始化
int limits[5] = {1200, 1200, 1200, 1200, 1200};  // 初始化

// 访问和修改
status[0] = 150;  // 速度
status[1] = 120;  // 耐力

// 遍历
for (int i = 0; i < 5; i++) {
    std::cout << "属性" << i << ": " << status[i] << std::endl;
}
```

---

## 4. 指针和引用

### 4.1 引用 (Reference)

```cpp
// 引用是别名，不是新的变量
void improveStatus(int& status) {  // 引用参数
    status += 10;  // 直接修改原变量
}

int speed = 100;
improveStatus(speed);  // speed现在是110

// const引用 - 只读
void printStatus(const int& status) {
    std::cout << "状态值: " << status << std::endl;
    // status += 10;  // 错误！不能修改const引用
}
```

### 4.2 指针 (Pointer)

```cpp
// 指针声明和使用
int vital = 100;
int* vital_ptr = &vital;  // 获取vital的地址

std::cout << *vital_ptr << std::endl;  // 解引用，输出100
*vital_ptr = 80;  // 通过指针修改值
std::cout << vital << std::endl;  // 输出80

// 空指针检查
if (vital_ptr != nullptr) {
    *vital_ptr = 90;
}
```

---

## 5. 函数和方法

### 5.1 函数定义

```cpp
// 函数声明
int calculateTrainingGain(int base_value, int head_count, bool is_shining);

// 函数实现
int calculateTrainingGain(int base_value, int head_count, bool is_shining) {
    int gain = base_value + head_count * 5;
    if (is_shining) {
        gain = static_cast<int>(gain * 1.5);  // 类型转换
    }
    return gain;
}

// 使用函数
int gain = calculateTrainingGain(20, 3, true);
```

### 5.2 函数重载

```cpp
// 同名函数，不同参数
void processTraining(int training_type) {
    // 处理基础训练
}

void processTraining(int training_type, int intensity) {
    // 处理带强度的训练
}

void processTraining(const std::string& training_name) {
    // 处理按名称的训练
}
```

### 5.3 默认参数

```cpp
void startGame(int character_id = 1, int difficulty = 2) {
    // character_id默认为1，difficulty默认为2
}

// 调用方式
startGame();           // 使用默认参数
startGame(5);          // character_id=5, difficulty=2
startGame(5, 3);       // character_id=5, difficulty=3
```

---

## 6. 枚举和常量

### 6.1 枚举 (enum)

```cpp
// 传统枚举
enum ActionType {
    TRAIN_SPEED = 0,
    TRAIN_STAMINA = 1,
    TRAIN_POWER = 2,
    REST = 5,
    OUTGOING = 6
};

// 强类型枚举 (C++11)
enum class CardRarity {
    R = 1,
    SR = 2,
    SSR = 3
};

// 使用枚举
ActionType action = TRAIN_SPEED;
CardRarity rarity = CardRarity::SSR;

// 枚举转换
int action_value = static_cast<int>(action);
```

### 6.2 常量

```cpp
// const常量
const int MAX_TURN = 78;
const double SHINING_BONUS = 1.5;

// constexpr常量 (编译时常量)
constexpr int ATTRIBUTE_COUNT = 5;
constexpr int MAX_VITAL = 100;

// 使用常量
if (turn >= MAX_TURN) {
    endGame();
}
```

---

## 7. 文件操作

### 7.1 读取文件

```cpp
#include <fstream>
#include <iostream>

// 读取文本文件
std::ifstream file("config.txt");
if (file.is_open()) {
    std::string line;
    while (std::getline(file, line)) {
        std::cout << line << std::endl;
    }
    file.close();
}

// 读取JSON文件 (需要JSON库)
#include <nlohmann/json.hpp>
std::ifstream json_file("data.json");
nlohmann::json data;
json_file >> data;

int character_id = data["character_id"];
std::string name = data["name"];
```

### 7.2 写入文件

```cpp
// 写入文本文件
std::ofstream output("result.txt");
if (output.is_open()) {
    output << "最终得分: " << final_score << std::endl;
    output << "比赛胜利: " << win_count << "/" << total_races << std::endl;
    output.close();
}
```

---

## 8. 随机数生成

### 8.1 现代C++随机数

```cpp
#include <random>

// 创建随机数生成器
std::mt19937 rng(std::time(nullptr));  // 使用当前时间作为种子

// 生成随机整数
std::uniform_int_distribution<int> dist(1, 100);
int random_value = dist(rng);

// 生成随机布尔值
bool success = (rng() % 100) < success_rate;

// 随机选择
std::vector<int> choices = {1, 2, 3, 4, 5};
int random_choice = choices[rng() % choices.size()];
```

### 8.2 随机数在游戏中的应用

```cpp
class GameSimulator {
private:
    std::mt19937 rng;
    
public:
    GameSimulator() : rng(std::time(nullptr)) {}
    
    bool isTrainingSuccess(int fail_rate) {
        return (rng() % 100) >= fail_rate;
    }
    
    int getRandomEvent() {
        return rng() % event_count;
    }
    
    void shuffleCards(std::vector<int>& cards) {
        std::shuffle(cards.begin(), cards.end(), rng);
    }
};
```

---

## 9. 项目实战技巧

### 9.1 错误处理

```cpp
// 使用返回值表示成功/失败
bool executeAction(const Action& action) {
    if (!isActionValid(action)) {
        return false;  // 动作无效
    }
    
    // 执行动作逻辑
    processAction(action);
    return true;  // 成功
}

// 使用异常处理 (谨慎使用)
try {
    int result = riskyOperation();
} catch (const std::exception& e) {
    std::cout << "错误: " << e.what() << std::endl;
}
```

### 9.2 调试技巧

```cpp
// 使用cout输出调试信息
std::cout << "当前回合: " << turn << ", 体力: " << vital << std::endl;

// 条件编译调试代码
#ifdef DEBUG
    std::cout << "调试信息: " << debug_info << std::endl;
#endif

// 断言 (Debug模式下检查条件)
#include <cassert>
assert(turn >= 0 && turn < 78);  // 确保回合数有效
```

### 9.3 代码组织

```cpp
// 头文件 (.h)
#pragma once  // 防止重复包含

class GameSimulator {
public:
    void startGame();
    bool executeAction(const Action& action);
    
private:
    URAGame game_state;
    void processTraining(int type);
};

// 实现文件 (.cpp)
#include "GameSimulator.h"

void GameSimulator::startGame() {
    game_state = URAGame();
}

bool GameSimulator::executeAction(const Action& action) {
    // 实现细节
    return true;
}
```

---

## 🎯 学习建议

### 优先掌握的概念
1. **结构体和基本语法** - 理解游戏状态表示
2. **函数和方法** - 实现游戏逻辑
3. **容器 (vector)** - 管理动态数据
4. **枚举** - 表示游戏中的选择和状态
5. **随机数** - 实现游戏的随机性

### 学习方法
1. **边学边练** - 每学一个概念就在项目中使用
2. **阅读项目代码** - 理解现有代码的实现
3. **小步修改** - 每次只改一点点，立即测试
4. **多写注释** - 帮助理解和记忆

### 常用参考
- [cppreference.com](https://en.cppreference.com/) - C++标准库参考
- Visual Studio的智能提示和错误信息
- 项目中的现有代码示例

---

## 🚀 下一步

学完这个文档后，请：
1. 完成配套的练习题
2. 尝试修改项目中的简单功能
3. 逐步理解更复杂的代码结构
4. 开始实现自己的功能

记住：**实践是最好的学习方法！** 💪
