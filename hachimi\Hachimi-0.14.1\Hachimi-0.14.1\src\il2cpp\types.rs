/* automatically generated by rust-bindgen 0.69.4 */
#![allow(non_upper_case_globals, non_snake_case, non_camel_case_types, dead_code)]

use serde::{Deserialize, Serialize};

#[repr(C)]
#[derive(<PERSON><PERSON>, <PERSON>lone, <PERSON>bug, <PERSON><PERSON>ult, Eq, Hash, Ord, PartialEq, PartialOrd)]
pub struct __BindgenBitfieldUnit<Storage> {
    storage: Storage,
}
impl<Storage> __BindgenBitfieldUnit<Storage> {
    #[inline]
    pub const fn new(storage: Storage) -> Self {
        Self { storage }
    }
}
impl<Storage> __BindgenBitfieldUnit<Storage>
where
    Storage: AsRef<[u8]> + AsMut<[u8]>,
{
    #[inline]
    pub fn get_bit(&self, index: usize) -> bool {
        debug_assert!(index / 8 < self.storage.as_ref().len());
        let byte_index = index / 8;
        let byte = self.storage.as_ref()[byte_index];
        let bit_index = if cfg!(target_endian = "big") {
            7 - (index % 8)
        } else {
            index % 8
        };
        let mask = 1 << bit_index;
        byte & mask == mask
    }
    #[inline]
    pub fn set_bit(&mut self, index: usize, val: bool) {
        debug_assert!(index / 8 < self.storage.as_ref().len());
        let byte_index = index / 8;
        let byte = &mut self.storage.as_mut()[byte_index];
        let bit_index = if cfg!(target_endian = "big") {
            7 - (index % 8)
        } else {
            index % 8
        };
        let mask = 1 << bit_index;
        if val {
            *byte |= mask;
        } else {
            *byte &= !mask;
        }
    }
    #[inline]
    pub fn get(&self, bit_offset: usize, bit_width: u8) -> u64 {
        debug_assert!(bit_width <= 64);
        debug_assert!(bit_offset / 8 < self.storage.as_ref().len());
        debug_assert!((bit_offset + (bit_width as usize)) / 8 <= self.storage.as_ref().len());
        let mut val = 0;
        for i in 0..(bit_width as usize) {
            if self.get_bit(i + bit_offset) {
                let index = if cfg!(target_endian = "big") {
                    bit_width as usize - 1 - i
                } else {
                    i
                };
                val |= 1 << index;
            }
        }
        val
    }
    #[inline]
    pub fn set(&mut self, bit_offset: usize, bit_width: u8, val: u64) {
        debug_assert!(bit_width <= 64);
        debug_assert!(bit_offset / 8 < self.storage.as_ref().len());
        debug_assert!((bit_offset + (bit_width as usize)) / 8 <= self.storage.as_ref().len());
        for i in 0..(bit_width as usize) {
            let mask = 1 << i;
            let val_bit_is_set = val & mask == mask;
            let index = if cfg!(target_endian = "big") {
                bit_width as usize - 1 - i
            } else {
                i
            };
            self.set_bit(index + bit_offset, val_bit_is_set);
        }
    }
}
#[repr(C)]
#[derive(Default)]
pub struct __IncompleteArrayField<T>(::std::marker::PhantomData<T>, [T; 0]);
impl<T> __IncompleteArrayField<T> {
    #[inline]
    pub const fn new() -> Self {
        __IncompleteArrayField(::std::marker::PhantomData, [])
    }
    #[inline]
    pub fn as_ptr(&self) -> *const T {
        self as *const _ as *const T
    }
    #[inline]
    pub fn as_mut_ptr(&mut self) -> *mut T {
        self as *mut _ as *mut T
    }
    #[inline]
    pub unsafe fn as_slice(&self, len: usize) -> &[T] {
        ::std::slice::from_raw_parts(self.as_ptr(), len)
    }
    #[inline]
    pub unsafe fn as_mut_slice(&mut self, len: usize) -> &mut [T] {
        ::std::slice::from_raw_parts_mut(self.as_mut_ptr(), len)
    }
}
impl<T> ::std::fmt::Debug for __IncompleteArrayField<T> {
    fn fmt(&self, fmt: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        fmt.write_str("__IncompleteArrayField")
    }
}
#[repr(C)]
pub struct __BindgenUnionField<T>(::std::marker::PhantomData<T>);
impl<T> __BindgenUnionField<T> {
    #[inline]
    pub const fn new() -> Self {
        __BindgenUnionField(::std::marker::PhantomData)
    }
    #[inline]
    pub unsafe fn as_ref(&self) -> &T {
        ::std::mem::transmute(self)
    }
    #[inline]
    pub unsafe fn as_mut(&mut self) -> &mut T {
        ::std::mem::transmute(self)
    }
}
impl<T> ::std::default::Default for __BindgenUnionField<T> {
    #[inline]
    fn default() -> Self {
        Self::new()
    }
}
impl<T> ::std::clone::Clone for __BindgenUnionField<T> {
    #[inline]
    fn clone(&self) -> Self {
        *self
    }
}
impl<T> ::std::marker::Copy for __BindgenUnionField<T> {}
impl<T> ::std::fmt::Debug for __BindgenUnionField<T> {
    fn fmt(&self, fmt: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        fmt.write_str("__BindgenUnionField")
    }
}
impl<T> ::std::hash::Hash for __BindgenUnionField<T> {
    fn hash<H: ::std::hash::Hasher>(&self, _state: &mut H) {}
}
impl<T> ::std::cmp::PartialEq for __BindgenUnionField<T> {
    fn eq(&self, _other: &__BindgenUnionField<T>) -> bool {
        true
    }
}
impl<T> ::std::cmp::Eq for __BindgenUnionField<T> {}

pub type __u_char = ::std::os::raw::c_uchar;
pub type __u_short = ::std::os::raw::c_ushort;
pub type __u_int = ::std::os::raw::c_uint;
pub type __u_long = ::std::os::raw::c_ulong;
pub type __int8_t = ::std::os::raw::c_schar;
pub type __uint8_t = ::std::os::raw::c_uchar;
pub type __int16_t = ::std::os::raw::c_short;
pub type __uint16_t = ::std::os::raw::c_ushort;
pub type __int32_t = ::std::os::raw::c_int;
pub type __uint32_t = ::std::os::raw::c_uint;
pub type __int64_t = ::std::os::raw::c_long;
pub type __uint64_t = ::std::os::raw::c_ulong;
#[repr(C)]
#[repr(align(16))]
#[derive(Debug)]
pub struct max_align_t {
    pub __clang_max_align_nonce1: ::std::os::raw::c_longlong,
    pub __bindgen_padding_0: u64,
    pub __clang_max_align_nonce2: u128,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppProfiler {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppManagedMemorySnapshot {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppCustomAttrInfo {
    _unused: [u8; 0],
}
pub const Il2CppProfileFlags_IL2CPP_PROFILE_NONE: Il2CppProfileFlags = 0;
pub const Il2CppProfileFlags_IL2CPP_PROFILE_APPDOMAIN_EVENTS: Il2CppProfileFlags = 1;
pub const Il2CppProfileFlags_IL2CPP_PROFILE_ASSEMBLY_EVENTS: Il2CppProfileFlags = 2;
pub const Il2CppProfileFlags_IL2CPP_PROFILE_MODULE_EVENTS: Il2CppProfileFlags = 4;
pub const Il2CppProfileFlags_IL2CPP_PROFILE_CLASS_EVENTS: Il2CppProfileFlags = 8;
pub const Il2CppProfileFlags_IL2CPP_PROFILE_JIT_COMPILATION: Il2CppProfileFlags = 16;
pub const Il2CppProfileFlags_IL2CPP_PROFILE_INLINING: Il2CppProfileFlags = 32;
pub const Il2CppProfileFlags_IL2CPP_PROFILE_EXCEPTIONS: Il2CppProfileFlags = 64;
pub const Il2CppProfileFlags_IL2CPP_PROFILE_ALLOCATIONS: Il2CppProfileFlags = 128;
pub const Il2CppProfileFlags_IL2CPP_PROFILE_GC: Il2CppProfileFlags = 256;
pub const Il2CppProfileFlags_IL2CPP_PROFILE_THREADS: Il2CppProfileFlags = 512;
pub const Il2CppProfileFlags_IL2CPP_PROFILE_REMOTING: Il2CppProfileFlags = 1024;
pub const Il2CppProfileFlags_IL2CPP_PROFILE_TRANSITIONS: Il2CppProfileFlags = 2048;
pub const Il2CppProfileFlags_IL2CPP_PROFILE_ENTER_LEAVE: Il2CppProfileFlags = 4096;
pub const Il2CppProfileFlags_IL2CPP_PROFILE_COVERAGE: Il2CppProfileFlags = 8192;
pub const Il2CppProfileFlags_IL2CPP_PROFILE_INS_COVERAGE: Il2CppProfileFlags = 16384;
pub const Il2CppProfileFlags_IL2CPP_PROFILE_STATISTICAL: Il2CppProfileFlags = 32768;
pub const Il2CppProfileFlags_IL2CPP_PROFILE_METHOD_EVENTS: Il2CppProfileFlags = 65536;
pub const Il2CppProfileFlags_IL2CPP_PROFILE_MONITOR_EVENTS: Il2CppProfileFlags = 131072;
pub const Il2CppProfileFlags_IL2CPP_PROFILE_IOMAP_EVENTS: Il2CppProfileFlags = 262144;
pub const Il2CppProfileFlags_IL2CPP_PROFILE_GC_MOVES: Il2CppProfileFlags = 524288;
pub const Il2CppProfileFlags_IL2CPP_PROFILE_FILEIO: Il2CppProfileFlags = 1048576;
pub type Il2CppProfileFlags = ::std::os::raw::c_uint;
pub const Il2CppProfileFileIOKind_IL2CPP_PROFILE_FILEIO_WRITE: Il2CppProfileFileIOKind = 0;
pub const Il2CppProfileFileIOKind_IL2CPP_PROFILE_FILEIO_READ: Il2CppProfileFileIOKind = 1;
pub type Il2CppProfileFileIOKind = ::std::os::raw::c_uint;
pub const Il2CppGCEvent_IL2CPP_GC_EVENT_START: Il2CppGCEvent = 0;
pub const Il2CppGCEvent_IL2CPP_GC_EVENT_MARK_START: Il2CppGCEvent = 1;
pub const Il2CppGCEvent_IL2CPP_GC_EVENT_MARK_END: Il2CppGCEvent = 2;
pub const Il2CppGCEvent_IL2CPP_GC_EVENT_RECLAIM_START: Il2CppGCEvent = 3;
pub const Il2CppGCEvent_IL2CPP_GC_EVENT_RECLAIM_END: Il2CppGCEvent = 4;
pub const Il2CppGCEvent_IL2CPP_GC_EVENT_END: Il2CppGCEvent = 5;
pub const Il2CppGCEvent_IL2CPP_GC_EVENT_PRE_STOP_WORLD: Il2CppGCEvent = 6;
pub const Il2CppGCEvent_IL2CPP_GC_EVENT_POST_STOP_WORLD: Il2CppGCEvent = 7;
pub const Il2CppGCEvent_IL2CPP_GC_EVENT_PRE_START_WORLD: Il2CppGCEvent = 8;
pub const Il2CppGCEvent_IL2CPP_GC_EVENT_POST_START_WORLD: Il2CppGCEvent = 9;
pub type Il2CppGCEvent = ::std::os::raw::c_uint;
pub const Il2CppGCMode_IL2CPP_GC_MODE_DISABLED: Il2CppGCMode = 0;
pub const Il2CppGCMode_IL2CPP_GC_MODE_ENABLED: Il2CppGCMode = 1;
pub const Il2CppGCMode_IL2CPP_GC_MODE_MANUAL: Il2CppGCMode = 2;
pub type Il2CppGCMode = ::std::os::raw::c_uint;
pub const Il2CppStat_IL2CPP_STAT_NEW_OBJECT_COUNT: Il2CppStat = 0;
pub const Il2CppStat_IL2CPP_STAT_INITIALIZED_CLASS_COUNT: Il2CppStat = 1;
pub const Il2CppStat_IL2CPP_STAT_METHOD_COUNT: Il2CppStat = 2;
pub const Il2CppStat_IL2CPP_STAT_CLASS_STATIC_DATA_SIZE: Il2CppStat = 3;
pub const Il2CppStat_IL2CPP_STAT_GENERIC_INSTANCE_COUNT: Il2CppStat = 4;
pub const Il2CppStat_IL2CPP_STAT_GENERIC_CLASS_COUNT: Il2CppStat = 5;
pub const Il2CppStat_IL2CPP_STAT_INFLATED_METHOD_COUNT: Il2CppStat = 6;
pub const Il2CppStat_IL2CPP_STAT_INFLATED_TYPE_COUNT: Il2CppStat = 7;
pub type Il2CppStat = ::std::os::raw::c_uint;
pub const Il2CppRuntimeUnhandledExceptionPolicy_IL2CPP_UNHANDLED_POLICY_LEGACY:
    Il2CppRuntimeUnhandledExceptionPolicy = 0;
pub const Il2CppRuntimeUnhandledExceptionPolicy_IL2CPP_UNHANDLED_POLICY_CURRENT:
    Il2CppRuntimeUnhandledExceptionPolicy = 1;
pub type Il2CppRuntimeUnhandledExceptionPolicy = ::std::os::raw::c_uint;
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppStackFrameInfo {
    pub method: *const MethodInfo,
    pub raw_ip: usize,
    pub sourceCodeLineNumber: ::std::os::raw::c_int,
    pub ilOffset: ::std::os::raw::c_int,
    pub filePath: *const ::std::os::raw::c_char,
}
pub type Il2CppMethodPointer = usize;
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppMethodDebugInfo {
    pub methodPointer: Il2CppMethodPointer,
    pub code_size: i32,
    pub file: *const ::std::os::raw::c_char,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppMemoryCallbacks {
    pub malloc_func:
        ::std::option::Option<unsafe extern "C" fn(size: usize) -> *mut ::std::os::raw::c_void>,
    pub aligned_malloc_func: ::std::option::Option<
        unsafe extern "C" fn(size: usize, alignment: usize) -> *mut ::std::os::raw::c_void,
    >,
    pub free_func: ::std::option::Option<unsafe extern "C" fn(ptr: *mut ::std::os::raw::c_void)>,
    pub aligned_free_func:
        ::std::option::Option<unsafe extern "C" fn(ptr: *mut ::std::os::raw::c_void)>,
    pub calloc_func: ::std::option::Option<
        unsafe extern "C" fn(nmemb: usize, size: usize) -> *mut ::std::os::raw::c_void,
    >,
    pub realloc_func: ::std::option::Option<
        unsafe extern "C" fn(
            ptr: *mut ::std::os::raw::c_void,
            size: usize,
        ) -> *mut ::std::os::raw::c_void,
    >,
    pub aligned_realloc_func: ::std::option::Option<
        unsafe extern "C" fn(
            ptr: *mut ::std::os::raw::c_void,
            size: usize,
            alignment: usize,
        ) -> *mut ::std::os::raw::c_void,
    >,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppDebuggerTransport {
    pub name: *const ::std::os::raw::c_char,
    pub connect:
        ::std::option::Option<unsafe extern "C" fn(address: *const ::std::os::raw::c_char)>,
    pub wait_for_attach: ::std::option::Option<unsafe extern "C" fn() -> ::std::os::raw::c_int>,
    pub close1: ::std::option::Option<unsafe extern "C" fn()>,
    pub close2: ::std::option::Option<unsafe extern "C" fn()>,
    pub send: ::std::option::Option<
        unsafe extern "C" fn(
            buf: *mut ::std::os::raw::c_void,
            len: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    >,
    pub recv: ::std::option::Option<
        unsafe extern "C" fn(
            buf: *mut ::std::os::raw::c_void,
            len: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    >,
}
pub type Il2CppChar = u16;
pub type Il2CppNativeChar = ::std::os::raw::c_char;
pub type il2cpp_register_object_callback = ::std::option::Option<
    unsafe extern "C" fn(
        arr: *mut *mut Il2CppObject,
        size: ::std::os::raw::c_int,
        userdata: *mut ::std::os::raw::c_void,
    ),
>;
pub type il2cpp_liveness_reallocate_callback = ::std::option::Option<
    unsafe extern "C" fn(
        ptr: *mut ::std::os::raw::c_void,
        size: usize,
        userdata: *mut ::std::os::raw::c_void,
    ) -> *mut ::std::os::raw::c_void,
>;
pub type Il2CppFrameWalkFunc = ::std::option::Option<
    unsafe extern "C" fn(info: *const Il2CppStackFrameInfo, user_data: *mut ::std::os::raw::c_void),
>;
pub type Il2CppProfileFunc = ::std::option::Option<unsafe extern "C" fn(prof: *mut Il2CppProfiler)>;
pub type Il2CppProfileMethodFunc = ::std::option::Option<
    unsafe extern "C" fn(prof: *mut Il2CppProfiler, method: *const MethodInfo),
>;
pub type Il2CppProfileAllocFunc = ::std::option::Option<
    unsafe extern "C" fn(
        prof: *mut Il2CppProfiler,
        obj: *mut Il2CppObject,
        klass: *mut Il2CppClass,
    ),
>;
pub type Il2CppProfileGCFunc = ::std::option::Option<
    unsafe extern "C" fn(
        prof: *mut Il2CppProfiler,
        event: Il2CppGCEvent,
        generation: ::std::os::raw::c_int,
    ),
>;
pub type Il2CppProfileGCResizeFunc =
    ::std::option::Option<unsafe extern "C" fn(prof: *mut Il2CppProfiler, new_size: i64)>;
pub type Il2CppProfileFileIOFunc = ::std::option::Option<
    unsafe extern "C" fn(
        prof: *mut Il2CppProfiler,
        kind: Il2CppProfileFileIOKind,
        count: ::std::os::raw::c_int,
    ),
>;
pub type Il2CppProfileThreadFunc = ::std::option::Option<
    unsafe extern "C" fn(prof: *mut Il2CppProfiler, tid: ::std::os::raw::c_ulong),
>;
pub type Il2CppSetFindPlugInCallback = ::std::option::Option<
    unsafe extern "C" fn(arg1: *const Il2CppNativeChar) -> *const Il2CppNativeChar,
>;
pub type Il2CppLogCallback =
    ::std::option::Option<unsafe extern "C" fn(arg1: *const ::std::os::raw::c_char)>;
pub type Il2CppBacktraceFunc = ::std::option::Option<
    unsafe extern "C" fn(buffer: *mut Il2CppMethodPointer, maxSize: usize) -> usize,
>;
pub type il2cpp_array_size_t = usize;
pub type Il2CppAndroidUpStateFunc = ::std::option::Option<
    unsafe extern "C" fn(ifName: *const ::std::os::raw::c_char, is_up: *mut u8) -> u8,
>;
pub type SynchronizationContextCallback = ::std::option::Option<unsafe extern "C" fn(arg: isize)>;
pub type CultureInfoChangedCallback =
    ::std::option::Option<unsafe extern "C" fn(arg: *const Il2CppChar)>;
pub type Il2CppMethodSlot = u16;
pub const kInvalidIl2CppMethodSlot: u16 = 65535;
pub const ipv6AddressSize: ::std::os::raw::c_int = 16;
pub type il2cpp_hresult_t = i32;
extern "C" {
    pub static kIl2CppNewLine: [Il2CppChar; 2usize];
}
pub const Il2CppTypeEnum_IL2CPP_TYPE_END: Il2CppTypeEnum = 0;
pub const Il2CppTypeEnum_IL2CPP_TYPE_VOID: Il2CppTypeEnum = 1;
pub const Il2CppTypeEnum_IL2CPP_TYPE_BOOLEAN: Il2CppTypeEnum = 2;
pub const Il2CppTypeEnum_IL2CPP_TYPE_CHAR: Il2CppTypeEnum = 3;
pub const Il2CppTypeEnum_IL2CPP_TYPE_I1: Il2CppTypeEnum = 4;
pub const Il2CppTypeEnum_IL2CPP_TYPE_U1: Il2CppTypeEnum = 5;
pub const Il2CppTypeEnum_IL2CPP_TYPE_I2: Il2CppTypeEnum = 6;
pub const Il2CppTypeEnum_IL2CPP_TYPE_U2: Il2CppTypeEnum = 7;
pub const Il2CppTypeEnum_IL2CPP_TYPE_I4: Il2CppTypeEnum = 8;
pub const Il2CppTypeEnum_IL2CPP_TYPE_U4: Il2CppTypeEnum = 9;
pub const Il2CppTypeEnum_IL2CPP_TYPE_I8: Il2CppTypeEnum = 10;
pub const Il2CppTypeEnum_IL2CPP_TYPE_U8: Il2CppTypeEnum = 11;
pub const Il2CppTypeEnum_IL2CPP_TYPE_R4: Il2CppTypeEnum = 12;
pub const Il2CppTypeEnum_IL2CPP_TYPE_R8: Il2CppTypeEnum = 13;
pub const Il2CppTypeEnum_IL2CPP_TYPE_STRING: Il2CppTypeEnum = 14;
pub const Il2CppTypeEnum_IL2CPP_TYPE_PTR: Il2CppTypeEnum = 15;
pub const Il2CppTypeEnum_IL2CPP_TYPE_BYREF: Il2CppTypeEnum = 16;
pub const Il2CppTypeEnum_IL2CPP_TYPE_VALUETYPE: Il2CppTypeEnum = 17;
pub const Il2CppTypeEnum_IL2CPP_TYPE_CLASS: Il2CppTypeEnum = 18;
pub const Il2CppTypeEnum_IL2CPP_TYPE_VAR: Il2CppTypeEnum = 19;
pub const Il2CppTypeEnum_IL2CPP_TYPE_ARRAY: Il2CppTypeEnum = 20;
pub const Il2CppTypeEnum_IL2CPP_TYPE_GENERICINST: Il2CppTypeEnum = 21;
pub const Il2CppTypeEnum_IL2CPP_TYPE_TYPEDBYREF: Il2CppTypeEnum = 22;
pub const Il2CppTypeEnum_IL2CPP_TYPE_I: Il2CppTypeEnum = 24;
pub const Il2CppTypeEnum_IL2CPP_TYPE_U: Il2CppTypeEnum = 25;
pub const Il2CppTypeEnum_IL2CPP_TYPE_FNPTR: Il2CppTypeEnum = 27;
pub const Il2CppTypeEnum_IL2CPP_TYPE_OBJECT: Il2CppTypeEnum = 28;
pub const Il2CppTypeEnum_IL2CPP_TYPE_SZARRAY: Il2CppTypeEnum = 29;
pub const Il2CppTypeEnum_IL2CPP_TYPE_MVAR: Il2CppTypeEnum = 30;
pub const Il2CppTypeEnum_IL2CPP_TYPE_CMOD_REQD: Il2CppTypeEnum = 31;
pub const Il2CppTypeEnum_IL2CPP_TYPE_CMOD_OPT: Il2CppTypeEnum = 32;
pub const Il2CppTypeEnum_IL2CPP_TYPE_INTERNAL: Il2CppTypeEnum = 33;
pub const Il2CppTypeEnum_IL2CPP_TYPE_MODIFIER: Il2CppTypeEnum = 64;
pub const Il2CppTypeEnum_IL2CPP_TYPE_SENTINEL: Il2CppTypeEnum = 65;
pub const Il2CppTypeEnum_IL2CPP_TYPE_PINNED: Il2CppTypeEnum = 69;
pub const Il2CppTypeEnum_IL2CPP_TYPE_ENUM: Il2CppTypeEnum = 85;
pub const Il2CppTypeEnum_IL2CPP_TYPE_IL2CPP_TYPE_INDEX: Il2CppTypeEnum = 255;
pub type Il2CppTypeEnum = ::std::os::raw::c_uint;
pub const Il2CppTokenType_IL2CPP_TOKEN_MODULE: Il2CppTokenType = 0;
pub const Il2CppTokenType_IL2CPP_TOKEN_TYPE_REF: Il2CppTokenType = 16777216;
pub const Il2CppTokenType_IL2CPP_TOKEN_TYPE_DEF: Il2CppTokenType = 33554432;
pub const Il2CppTokenType_IL2CPP_TOKEN_FIELD_DEF: Il2CppTokenType = 67108864;
pub const Il2CppTokenType_IL2CPP_TOKEN_METHOD_DEF: Il2CppTokenType = 100663296;
pub const Il2CppTokenType_IL2CPP_TOKEN_PARAM_DEF: Il2CppTokenType = 134217728;
pub const Il2CppTokenType_IL2CPP_TOKEN_INTERFACE_IMPL: Il2CppTokenType = 150994944;
pub const Il2CppTokenType_IL2CPP_TOKEN_MEMBER_REF: Il2CppTokenType = 167772160;
pub const Il2CppTokenType_IL2CPP_TOKEN_CUSTOM_ATTRIBUTE: Il2CppTokenType = 201326592;
pub const Il2CppTokenType_IL2CPP_TOKEN_PERMISSION: Il2CppTokenType = 234881024;
pub const Il2CppTokenType_IL2CPP_TOKEN_SIGNATURE: Il2CppTokenType = 285212672;
pub const Il2CppTokenType_IL2CPP_TOKEN_EVENT: Il2CppTokenType = 335544320;
pub const Il2CppTokenType_IL2CPP_TOKEN_PROPERTY: Il2CppTokenType = 385875968;
pub const Il2CppTokenType_IL2CPP_TOKEN_MODULE_REF: Il2CppTokenType = 436207616;
pub const Il2CppTokenType_IL2CPP_TOKEN_TYPE_SPEC: Il2CppTokenType = 452984832;
pub const Il2CppTokenType_IL2CPP_TOKEN_ASSEMBLY: Il2CppTokenType = 536870912;
pub const Il2CppTokenType_IL2CPP_TOKEN_ASSEMBLY_REF: Il2CppTokenType = 587202560;
pub const Il2CppTokenType_IL2CPP_TOKEN_FILE: Il2CppTokenType = 637534208;
pub const Il2CppTokenType_IL2CPP_TOKEN_EXPORTED_TYPE: Il2CppTokenType = 654311424;
pub const Il2CppTokenType_IL2CPP_TOKEN_MANIFEST_RESOURCE: Il2CppTokenType = 671088640;
pub const Il2CppTokenType_IL2CPP_TOKEN_GENERIC_PARAM: Il2CppTokenType = 704643072;
pub const Il2CppTokenType_IL2CPP_TOKEN_METHOD_SPEC: Il2CppTokenType = 721420288;
pub type Il2CppTokenType = ::std::os::raw::c_uint;
pub type TypeIndex = i32;
pub type TypeDefinitionIndex = i32;
pub type FieldIndex = i32;
pub type DefaultValueIndex = i32;
pub type DefaultValueDataIndex = i32;
pub type CustomAttributeIndex = i32;
pub type ParameterIndex = i32;
pub type MethodIndex = i32;
pub type GenericMethodIndex = i32;
pub type PropertyIndex = i32;
pub type EventIndex = i32;
pub type GenericContainerIndex = i32;
pub type GenericParameterIndex = i32;
pub type GenericParameterConstraintIndex = i16;
pub type NestedTypeIndex = i32;
pub type InterfacesIndex = i32;
pub type VTableIndex = i32;
pub type RGCTXIndex = i32;
pub type StringIndex = i32;
pub type StringLiteralIndex = i32;
pub type GenericInstIndex = i32;
pub type ImageIndex = i32;
pub type AssemblyIndex = i32;
pub type InteropDataIndex = i32;
pub type TypeFieldIndex = i32;
pub type TypeMethodIndex = i32;
pub type MethodParameterIndex = i32;
pub type TypePropertyIndex = i32;
pub type TypeEventIndex = i32;
pub type TypeInterfaceIndex = i32;
pub type TypeNestedTypeIndex = i32;
pub type TypeInterfaceOffsetIndex = i32;
pub type GenericContainerParameterIndex = i32;
pub type AssemblyTypeIndex = i32;
pub type AssemblyExportedTypeIndex = i32;
pub const kTypeIndexInvalid: TypeIndex = -1;
pub const kTypeDefinitionIndexInvalid: TypeDefinitionIndex = -1;
pub const kDefaultValueIndexNull: DefaultValueDataIndex = -1;
pub const kCustomAttributeIndexInvalid: CustomAttributeIndex = -1;
pub const kEventIndexInvalid: EventIndex = -1;
pub const kFieldIndexInvalid: FieldIndex = -1;
pub const kMethodIndexInvalid: MethodIndex = -1;
pub const kPropertyIndexInvalid: PropertyIndex = -1;
pub const kGenericContainerIndexInvalid: GenericContainerIndex = -1;
pub const kGenericParameterIndexInvalid: GenericParameterIndex = -1;
pub const kRGCTXIndexInvalid: RGCTXIndex = -1;
pub const kStringLiteralIndexInvalid: StringLiteralIndex = -1;
pub const kInteropDataIndexInvalid: InteropDataIndex = -1;
pub const kPublicKeyByteLength: ::std::os::raw::c_int = 8;
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppMethodSpec {
    pub methodDefinitionIndex: MethodIndex,
    pub classIndexIndex: GenericInstIndex,
    pub methodIndexIndex: GenericInstIndex,
}
pub const Il2CppRGCTXDataType_IL2CPP_RGCTX_DATA_INVALID: Il2CppRGCTXDataType = 0;
pub const Il2CppRGCTXDataType_IL2CPP_RGCTX_DATA_TYPE: Il2CppRGCTXDataType = 1;
pub const Il2CppRGCTXDataType_IL2CPP_RGCTX_DATA_CLASS: Il2CppRGCTXDataType = 2;
pub const Il2CppRGCTXDataType_IL2CPP_RGCTX_DATA_METHOD: Il2CppRGCTXDataType = 3;
pub const Il2CppRGCTXDataType_IL2CPP_RGCTX_DATA_ARRAY: Il2CppRGCTXDataType = 4;
pub const Il2CppRGCTXDataType_IL2CPP_RGCTX_DATA_CONSTRAINED: Il2CppRGCTXDataType = 5;
pub type Il2CppRGCTXDataType = ::std::os::raw::c_uint;
#[repr(C)]
pub struct Il2CppRGCTXDefinitionData {
    pub rgctxDataDummy: __BindgenUnionField<i32>,
    pub __methodIndex: __BindgenUnionField<MethodIndex>,
    pub __typeIndex: __BindgenUnionField<TypeIndex>,
    pub bindgen_union_field: u32,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppRGCTXConstrainedData {
    pub __typeIndex: TypeIndex,
    pub __encodedMethodIndex: u32,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppRGCTXDefinition {
    pub type_: Il2CppRGCTXDataType,
    pub data: *const ::std::os::raw::c_void,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppGenericMethodIndices {
    pub methodIndex: MethodIndex,
    pub invokerIndex: MethodIndex,
    pub adjustorThunkIndex: MethodIndex,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppGenericMethodFunctionsDefinitions {
    pub genericMethodIndex: GenericMethodIndex,
    pub indices: Il2CppGenericMethodIndices,
}
#[repr(C)]
#[derive(Debug)]
pub struct ___Il2CppMetadataImageHandle {
    _unused: [u8; 0],
}
pub type Il2CppMetadataImageHandle = *const ___Il2CppMetadataImageHandle;
#[repr(C)]
#[derive(Debug)]
pub struct ___Il2CppMetadataCustomAttributeHandle {
    _unused: [u8; 0],
}
pub type Il2CppMetadataCustomAttributeHandle = *const ___Il2CppMetadataCustomAttributeHandle;
#[repr(C)]
#[derive(Debug)]
pub struct ___Il2CppMetadataTypeHandle {
    _unused: [u8; 0],
}
pub type Il2CppMetadataTypeHandle = *const ___Il2CppMetadataTypeHandle;
#[repr(C)]
#[derive(Debug)]
pub struct ___Il2CppMetadataMethodHandle {
    _unused: [u8; 0],
}
pub type Il2CppMetadataMethodDefinitionHandle = *const ___Il2CppMetadataMethodHandle;
#[repr(C)]
#[derive(Debug)]
pub struct ___Il2CppMetadataGenericContainerHandle {
    _unused: [u8; 0],
}
pub type Il2CppMetadataGenericContainerHandle = *const ___Il2CppMetadataGenericContainerHandle;
#[repr(C)]
#[derive(Debug)]
pub struct ___Il2CppMetadataGenericParameterHandle {
    _unused: [u8; 0],
}
pub type Il2CppMetadataGenericParameterHandle = *const ___Il2CppMetadataGenericParameterHandle;
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppArrayType {
    pub etype: *const Il2CppType,
    pub rank: u8,
    pub numsizes: u8,
    pub numlobounds: u8,
    pub sizes: *mut ::std::os::raw::c_int,
    pub lobounds: *mut ::std::os::raw::c_int,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppGenericInst {
    pub type_argc: u32,
    pub type_argv: *mut *const Il2CppType,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppGenericContext {
    pub class_inst: *const Il2CppGenericInst,
    pub method_inst: *const Il2CppGenericInst,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppGenericClass {
    pub type_: *const Il2CppType,
    pub context: Il2CppGenericContext,
    pub cached_class: *mut Il2CppClass,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppGenericMethod {
    pub methodDefinition: *const MethodInfo,
    pub context: Il2CppGenericContext,
}
#[repr(C)]
pub struct Il2CppType {
    pub data: Il2CppType__bindgen_ty_1,
    pub _bitfield_align_1: [u16; 0],
    pub _bitfield_1: __BindgenBitfieldUnit<[u8; 4usize]>,
    pub __bindgen_padding_0: u32,
}
#[repr(C)]
pub struct Il2CppType__bindgen_ty_1 {
    pub dummy: __BindgenUnionField<*mut ::std::os::raw::c_void>,
    pub __klassIndex: __BindgenUnionField<TypeDefinitionIndex>,
    pub typeHandle: __BindgenUnionField<Il2CppMetadataTypeHandle>,
    pub type_: __BindgenUnionField<*const Il2CppType>,
    pub array: __BindgenUnionField<*mut Il2CppArrayType>,
    pub __genericParameterIndex: __BindgenUnionField<GenericParameterIndex>,
    pub genericParameterHandle: __BindgenUnionField<Il2CppMetadataGenericParameterHandle>,
    pub generic_class: __BindgenUnionField<*mut Il2CppGenericClass>,
    pub bindgen_union_field: usize,
}
impl Il2CppType {
    #[inline]
    pub fn attrs(&self) -> ::std::os::raw::c_uint {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(0usize, 16u8) as u32) }
    }
    #[inline]
    pub fn set_attrs(&mut self, val: ::std::os::raw::c_uint) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            self._bitfield_1.set(0usize, 16u8, val as u64)
        }
    }
    #[inline]
    pub fn type_(&self) -> Il2CppTypeEnum {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(16usize, 8u8) as u32) }
    }
    #[inline]
    pub fn set_type(&mut self, val: Il2CppTypeEnum) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            self._bitfield_1.set(16usize, 8u8, val as u64)
        }
    }
    #[inline]
    pub fn num_mods(&self) -> ::std::os::raw::c_uint {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(24usize, 5u8) as u32) }
    }
    #[inline]
    pub fn set_num_mods(&mut self, val: ::std::os::raw::c_uint) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            self._bitfield_1.set(24usize, 5u8, val as u64)
        }
    }
    #[inline]
    pub fn byref(&self) -> ::std::os::raw::c_uint {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(29usize, 1u8) as u32) }
    }
    #[inline]
    pub fn set_byref(&mut self, val: ::std::os::raw::c_uint) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            self._bitfield_1.set(29usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub fn pinned(&self) -> ::std::os::raw::c_uint {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(30usize, 1u8) as u32) }
    }
    #[inline]
    pub fn set_pinned(&mut self, val: ::std::os::raw::c_uint) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            self._bitfield_1.set(30usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub fn valuetype(&self) -> ::std::os::raw::c_uint {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(31usize, 1u8) as u32) }
    }
    #[inline]
    pub fn set_valuetype(&mut self, val: ::std::os::raw::c_uint) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            self._bitfield_1.set(31usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub fn new_bitfield_1(
        attrs: ::std::os::raw::c_uint,
        type_: Il2CppTypeEnum,
        num_mods: ::std::os::raw::c_uint,
        byref: ::std::os::raw::c_uint,
        pinned: ::std::os::raw::c_uint,
        valuetype: ::std::os::raw::c_uint,
    ) -> __BindgenBitfieldUnit<[u8; 4usize]> {
        let mut __bindgen_bitfield_unit: __BindgenBitfieldUnit<[u8; 4usize]> = Default::default();
        __bindgen_bitfield_unit.set(0usize, 16u8, {
            let attrs: u32 = unsafe { ::std::mem::transmute(attrs) };
            attrs as u64
        });
        __bindgen_bitfield_unit.set(16usize, 8u8, {
            let type_: u32 = unsafe { ::std::mem::transmute(type_) };
            type_ as u64
        });
        __bindgen_bitfield_unit.set(24usize, 5u8, {
            let num_mods: u32 = unsafe { ::std::mem::transmute(num_mods) };
            num_mods as u64
        });
        __bindgen_bitfield_unit.set(29usize, 1u8, {
            let byref: u32 = unsafe { ::std::mem::transmute(byref) };
            byref as u64
        });
        __bindgen_bitfield_unit.set(30usize, 1u8, {
            let pinned: u32 = unsafe { ::std::mem::transmute(pinned) };
            pinned as u64
        });
        __bindgen_bitfield_unit.set(31usize, 1u8, {
            let valuetype: u32 = unsafe { ::std::mem::transmute(valuetype) };
            valuetype as u64
        });
        __bindgen_bitfield_unit
    }
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppMetadataFieldInfo {
    pub type_: *const Il2CppType,
    pub name: *const ::std::os::raw::c_char,
    pub token: u32,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppMetadataMethodInfo {
    pub handle: Il2CppMetadataMethodDefinitionHandle,
    pub name: *const ::std::os::raw::c_char,
    pub return_type: *const Il2CppType,
    pub token: u32,
    pub flags: u16,
    pub iflags: u16,
    pub slot: u16,
    pub parameterCount: u16,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppMetadataParameterInfo {
    pub name: *const ::std::os::raw::c_char,
    pub token: u32,
    pub type_: *const Il2CppType,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppMetadataPropertyInfo {
    pub name: *const ::std::os::raw::c_char,
    pub get: *const MethodInfo,
    pub set: *const MethodInfo,
    pub attrs: u32,
    pub token: u32,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppMetadataEventInfo {
    pub name: *const ::std::os::raw::c_char,
    pub type_: *const Il2CppType,
    pub add: *const MethodInfo,
    pub remove: *const MethodInfo,
    pub raise: *const MethodInfo,
    pub token: u32,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppInterfaceOffsetInfo {
    pub interfaceType: *const Il2CppType,
    pub offset: i32,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppGenericParameterInfo {
    pub containerHandle: Il2CppMetadataGenericContainerHandle,
    pub name: *const ::std::os::raw::c_char,
    pub num: u16,
    pub flags: u16,
}
pub const Il2CppCallConvention_IL2CPP_CALL_DEFAULT: Il2CppCallConvention = 0;
pub const Il2CppCallConvention_IL2CPP_CALL_C: Il2CppCallConvention = 1;
pub const Il2CppCallConvention_IL2CPP_CALL_STDCALL: Il2CppCallConvention = 2;
pub const Il2CppCallConvention_IL2CPP_CALL_THISCALL: Il2CppCallConvention = 3;
pub const Il2CppCallConvention_IL2CPP_CALL_FASTCALL: Il2CppCallConvention = 4;
pub const Il2CppCallConvention_IL2CPP_CALL_VARARG: Il2CppCallConvention = 5;
pub type Il2CppCallConvention = ::std::os::raw::c_uint;
pub const Il2CppCharSet_CHARSET_ANSI: Il2CppCharSet = 0;
pub const Il2CppCharSet_CHARSET_UNICODE: Il2CppCharSet = 1;
pub const Il2CppCharSet_CHARSET_NOT_SPECIFIED: Il2CppCharSet = 2;
pub type Il2CppCharSet = ::std::os::raw::c_uint;
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppHString__ {
    pub unused: ::std::os::raw::c_int,
}
pub type Il2CppHString = *mut Il2CppHString__;
#[repr(C)]
pub struct Il2CppHStringHeader {
    pub Reserved: Il2CppHStringHeader__bindgen_ty_1,
}
#[repr(C)]
pub struct Il2CppHStringHeader__bindgen_ty_1 {
    pub Reserved1: __BindgenUnionField<*mut ::std::os::raw::c_void>,
    pub Reserved2: __BindgenUnionField<[::std::os::raw::c_char; 24usize]>,
    pub bindgen_union_field: [u64; 3usize],
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppGuid {
    pub data1: u32,
    pub data2: u16,
    pub data3: u16,
    pub data4: [u8; 8usize],
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppSafeArrayBound {
    pub element_count: u32,
    pub lower_bound: i32,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppSafeArray {
    pub dimension_count: u16,
    pub features: u16,
    pub element_size: u32,
    pub lock_count: u32,
    pub data: *mut ::std::os::raw::c_void,
    pub bounds: [Il2CppSafeArrayBound; 1usize],
}
#[repr(C)]
pub struct Il2CppWin32Decimal {
    pub reserved: u16,
    pub u: Il2CppWin32Decimal__bindgen_ty_1,
    pub hi32: u32,
    pub u2: Il2CppWin32Decimal__bindgen_ty_2,
}
#[repr(C)]
pub struct Il2CppWin32Decimal__bindgen_ty_1 {
    pub s: __BindgenUnionField<Il2CppWin32Decimal__bindgen_ty_1__bindgen_ty_1>,
    pub signscale: __BindgenUnionField<u16>,
    pub bindgen_union_field: u16,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppWin32Decimal__bindgen_ty_1__bindgen_ty_1 {
    pub scale: u8,
    pub sign: u8,
}
#[repr(C)]
pub struct Il2CppWin32Decimal__bindgen_ty_2 {
    pub s2: __BindgenUnionField<Il2CppWin32Decimal__bindgen_ty_2__bindgen_ty_1>,
    pub lo64: __BindgenUnionField<u64>,
    pub bindgen_union_field: u64,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppWin32Decimal__bindgen_ty_2__bindgen_ty_1 {
    pub lo32: u32,
    pub mid32: u32,
}
pub type IL2CPP_VARIANT_BOOL = i16;
pub const Il2CppVarType_IL2CPP_VT_EMPTY: Il2CppVarType = 0;
pub const Il2CppVarType_IL2CPP_VT_NULL: Il2CppVarType = 1;
pub const Il2CppVarType_IL2CPP_VT_I2: Il2CppVarType = 2;
pub const Il2CppVarType_IL2CPP_VT_I4: Il2CppVarType = 3;
pub const Il2CppVarType_IL2CPP_VT_R4: Il2CppVarType = 4;
pub const Il2CppVarType_IL2CPP_VT_R8: Il2CppVarType = 5;
pub const Il2CppVarType_IL2CPP_VT_CY: Il2CppVarType = 6;
pub const Il2CppVarType_IL2CPP_VT_DATE: Il2CppVarType = 7;
pub const Il2CppVarType_IL2CPP_VT_BSTR: Il2CppVarType = 8;
pub const Il2CppVarType_IL2CPP_VT_DISPATCH: Il2CppVarType = 9;
pub const Il2CppVarType_IL2CPP_VT_ERROR: Il2CppVarType = 10;
pub const Il2CppVarType_IL2CPP_VT_BOOL: Il2CppVarType = 11;
pub const Il2CppVarType_IL2CPP_VT_VARIANT: Il2CppVarType = 12;
pub const Il2CppVarType_IL2CPP_VT_UNKNOWN: Il2CppVarType = 13;
pub const Il2CppVarType_IL2CPP_VT_DECIMAL: Il2CppVarType = 14;
pub const Il2CppVarType_IL2CPP_VT_I1: Il2CppVarType = 16;
pub const Il2CppVarType_IL2CPP_VT_UI1: Il2CppVarType = 17;
pub const Il2CppVarType_IL2CPP_VT_UI2: Il2CppVarType = 18;
pub const Il2CppVarType_IL2CPP_VT_UI4: Il2CppVarType = 19;
pub const Il2CppVarType_IL2CPP_VT_I8: Il2CppVarType = 20;
pub const Il2CppVarType_IL2CPP_VT_UI8: Il2CppVarType = 21;
pub const Il2CppVarType_IL2CPP_VT_INT: Il2CppVarType = 22;
pub const Il2CppVarType_IL2CPP_VT_UINT: Il2CppVarType = 23;
pub const Il2CppVarType_IL2CPP_VT_VOID: Il2CppVarType = 24;
pub const Il2CppVarType_IL2CPP_VT_HRESULT: Il2CppVarType = 25;
pub const Il2CppVarType_IL2CPP_VT_PTR: Il2CppVarType = 26;
pub const Il2CppVarType_IL2CPP_VT_SAFEARRAY: Il2CppVarType = 27;
pub const Il2CppVarType_IL2CPP_VT_CARRAY: Il2CppVarType = 28;
pub const Il2CppVarType_IL2CPP_VT_USERDEFINED: Il2CppVarType = 29;
pub const Il2CppVarType_IL2CPP_VT_LPSTR: Il2CppVarType = 30;
pub const Il2CppVarType_IL2CPP_VT_LPWSTR: Il2CppVarType = 31;
pub const Il2CppVarType_IL2CPP_VT_RECORD: Il2CppVarType = 36;
pub const Il2CppVarType_IL2CPP_VT_INT_PTR: Il2CppVarType = 37;
pub const Il2CppVarType_IL2CPP_VT_UINT_PTR: Il2CppVarType = 38;
pub const Il2CppVarType_IL2CPP_VT_FILETIME: Il2CppVarType = 64;
pub const Il2CppVarType_IL2CPP_VT_BLOB: Il2CppVarType = 65;
pub const Il2CppVarType_IL2CPP_VT_STREAM: Il2CppVarType = 66;
pub const Il2CppVarType_IL2CPP_VT_STORAGE: Il2CppVarType = 67;
pub const Il2CppVarType_IL2CPP_VT_STREAMED_OBJECT: Il2CppVarType = 68;
pub const Il2CppVarType_IL2CPP_VT_STORED_OBJECT: Il2CppVarType = 69;
pub const Il2CppVarType_IL2CPP_VT_BLOB_OBJECT: Il2CppVarType = 70;
pub const Il2CppVarType_IL2CPP_VT_CF: Il2CppVarType = 71;
pub const Il2CppVarType_IL2CPP_VT_CLSID: Il2CppVarType = 72;
pub const Il2CppVarType_IL2CPP_VT_VERSIONED_STREAM: Il2CppVarType = 73;
pub const Il2CppVarType_IL2CPP_VT_BSTR_BLOB: Il2CppVarType = 4095;
pub const Il2CppVarType_IL2CPP_VT_VECTOR: Il2CppVarType = 4096;
pub const Il2CppVarType_IL2CPP_VT_ARRAY: Il2CppVarType = 8192;
pub const Il2CppVarType_IL2CPP_VT_BYREF: Il2CppVarType = 16384;
pub const Il2CppVarType_IL2CPP_VT_RESERVED: Il2CppVarType = 32768;
pub const Il2CppVarType_IL2CPP_VT_ILLEGAL: Il2CppVarType = 65535;
pub const Il2CppVarType_IL2CPP_VT_ILLEGALMASKED: Il2CppVarType = 4095;
pub const Il2CppVarType_IL2CPP_VT_TYPEMASK: Il2CppVarType = 4095;
pub type Il2CppVarType = ::std::os::raw::c_uint;
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppIUnknown {
    _unused: [u8; 0],
}
#[repr(C)]
pub struct Il2CppVariant {
    pub n1: Il2CppVariant__bindgen_ty_1,
}
#[repr(C)]
pub struct Il2CppVariant__bindgen_ty_1 {
    pub n2: __BindgenUnionField<Il2CppVariant__bindgen_ty_1___tagVARIANT>,
    pub decVal: __BindgenUnionField<Il2CppWin32Decimal>,
    pub bindgen_union_field: [u64; 3usize],
}
#[repr(C)]
pub struct Il2CppVariant__bindgen_ty_1___tagVARIANT {
    pub type_: u16,
    pub reserved1: u16,
    pub reserved2: u16,
    pub reserved3: u16,
    pub n3: Il2CppVariant__bindgen_ty_1___tagVARIANT__bindgen_ty_1,
}
#[repr(C)]
pub struct Il2CppVariant__bindgen_ty_1___tagVARIANT__bindgen_ty_1 {
    pub llVal: __BindgenUnionField<i64>,
    pub lVal: __BindgenUnionField<i32>,
    pub bVal: __BindgenUnionField<u8>,
    pub iVal: __BindgenUnionField<i16>,
    pub fltVal: __BindgenUnionField<f32>,
    pub dblVal: __BindgenUnionField<f64>,
    pub boolVal: __BindgenUnionField<IL2CPP_VARIANT_BOOL>,
    pub scode: __BindgenUnionField<i32>,
    pub cyVal: __BindgenUnionField<i64>,
    pub date: __BindgenUnionField<f64>,
    pub bstrVal: __BindgenUnionField<*mut Il2CppChar>,
    pub punkVal: __BindgenUnionField<*mut Il2CppIUnknown>,
    pub pdispVal: __BindgenUnionField<*mut ::std::os::raw::c_void>,
    pub parray: __BindgenUnionField<*mut Il2CppSafeArray>,
    pub pbVal: __BindgenUnionField<*mut u8>,
    pub piVal: __BindgenUnionField<*mut i16>,
    pub plVal: __BindgenUnionField<*mut i32>,
    pub pllVal: __BindgenUnionField<*mut i64>,
    pub pfltVal: __BindgenUnionField<*mut f32>,
    pub pdblVal: __BindgenUnionField<*mut f64>,
    pub pboolVal: __BindgenUnionField<*mut IL2CPP_VARIANT_BOOL>,
    pub pscode: __BindgenUnionField<*mut i32>,
    pub pcyVal: __BindgenUnionField<*mut i64>,
    pub pdate: __BindgenUnionField<*mut f64>,
    pub pbstrVal: __BindgenUnionField<*mut Il2CppChar>,
    pub ppunkVal: __BindgenUnionField<*mut *mut Il2CppIUnknown>,
    pub ppdispVal: __BindgenUnionField<*mut *mut ::std::os::raw::c_void>,
    pub pparray: __BindgenUnionField<*mut *mut Il2CppSafeArray>,
    pub pvarVal: __BindgenUnionField<*mut Il2CppVariant>,
    pub byref: __BindgenUnionField<*mut ::std::os::raw::c_void>,
    pub cVal: __BindgenUnionField<::std::os::raw::c_char>,
    pub uiVal: __BindgenUnionField<u16>,
    pub ulVal: __BindgenUnionField<u32>,
    pub ullVal: __BindgenUnionField<u64>,
    pub intVal: __BindgenUnionField<::std::os::raw::c_int>,
    pub uintVal: __BindgenUnionField<::std::os::raw::c_uint>,
    pub pdecVal: __BindgenUnionField<*mut Il2CppWin32Decimal>,
    pub pcVal: __BindgenUnionField<*mut ::std::os::raw::c_char>,
    pub puiVal: __BindgenUnionField<*mut u16>,
    pub pulVal: __BindgenUnionField<*mut u32>,
    pub pullVal: __BindgenUnionField<*mut u64>,
    pub pintVal: __BindgenUnionField<*mut ::std::os::raw::c_int>,
    pub puintVal: __BindgenUnionField<*mut ::std::os::raw::c_uint>,
    pub n4:
        __BindgenUnionField<Il2CppVariant__bindgen_ty_1___tagVARIANT__bindgen_ty_1___tagBRECORD>,
    pub bindgen_union_field: [u64; 2usize],
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppVariant__bindgen_ty_1___tagVARIANT__bindgen_ty_1___tagBRECORD {
    pub pvRecord: *mut ::std::os::raw::c_void,
    pub pRecInfo: *mut ::std::os::raw::c_void,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppFileTime {
    pub low: u32,
    pub high: u32,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppStatStg {
    pub name: *mut Il2CppChar,
    pub type_: u32,
    pub size: u64,
    pub mtime: Il2CppFileTime,
    pub ctime: Il2CppFileTime,
    pub atime: Il2CppFileTime,
    pub mode: u32,
    pub locks: u32,
    pub clsid: Il2CppGuid,
    pub state: u32,
    pub reserved: u32,
}
pub const Il2CppWindowsRuntimeTypeKind_kTypeKindPrimitive: Il2CppWindowsRuntimeTypeKind = 0;
pub const Il2CppWindowsRuntimeTypeKind_kTypeKindMetadata: Il2CppWindowsRuntimeTypeKind = 1;
pub const Il2CppWindowsRuntimeTypeKind_kTypeKindCustom: Il2CppWindowsRuntimeTypeKind = 2;
pub type Il2CppWindowsRuntimeTypeKind = ::std::os::raw::c_uint;
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppWindowsRuntimeTypeName {
    pub typeName: Il2CppHString,
    pub typeKind: Il2CppWindowsRuntimeTypeKind,
}
pub type PInvokeMarshalToNativeFunc = ::std::option::Option<
    unsafe extern "C" fn(
        managedStructure: *mut ::std::os::raw::c_void,
        marshaledStructure: *mut ::std::os::raw::c_void,
    ),
>;
pub type PInvokeMarshalFromNativeFunc = ::std::option::Option<
    unsafe extern "C" fn(
        marshaledStructure: *mut ::std::os::raw::c_void,
        managedStructure: *mut ::std::os::raw::c_void,
    ),
>;
pub type PInvokeMarshalCleanupFunc =
    ::std::option::Option<unsafe extern "C" fn(marshaledStructure: *mut ::std::os::raw::c_void)>;
pub type CreateCCWFunc =
    ::std::option::Option<unsafe extern "C" fn(obj: *mut Il2CppObject) -> *mut Il2CppIUnknown>;
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppInteropData {
    pub delegatePInvokeWrapperFunction: Il2CppMethodPointer,
    pub pinvokeMarshalToNativeFunction: PInvokeMarshalToNativeFunc,
    pub pinvokeMarshalFromNativeFunction: PInvokeMarshalFromNativeFunc,
    pub pinvokeMarshalCleanupFunction: PInvokeMarshalCleanupFunc,
    pub createCCWFunction: CreateCCWFunc,
    pub guid: *const Il2CppGuid,
    pub type_: *const Il2CppType,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppNameToTypeHandleHashTable {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Debug)]
pub struct VirtualInvokeData {
    pub methodPtr: Il2CppMethodPointer,
    pub method: *const MethodInfo,
}
pub const Il2CppTypeNameFormat_IL2CPP_TYPE_NAME_FORMAT_IL: Il2CppTypeNameFormat = 0;
pub const Il2CppTypeNameFormat_IL2CPP_TYPE_NAME_FORMAT_REFLECTION: Il2CppTypeNameFormat = 1;
pub const Il2CppTypeNameFormat_IL2CPP_TYPE_NAME_FORMAT_FULL_NAME: Il2CppTypeNameFormat = 2;
pub const Il2CppTypeNameFormat_IL2CPP_TYPE_NAME_FORMAT_ASSEMBLY_QUALIFIED: Il2CppTypeNameFormat = 3;
pub type Il2CppTypeNameFormat = ::std::os::raw::c_uint;
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppDefaults {
    pub corlib: *mut Il2CppImage,
    pub corlib_gen: *mut Il2CppImage,
    pub object_class: *mut Il2CppClass,
    pub byte_class: *mut Il2CppClass,
    pub void_class: *mut Il2CppClass,
    pub boolean_class: *mut Il2CppClass,
    pub sbyte_class: *mut Il2CppClass,
    pub int16_class: *mut Il2CppClass,
    pub uint16_class: *mut Il2CppClass,
    pub int32_class: *mut Il2CppClass,
    pub uint32_class: *mut Il2CppClass,
    pub int_class: *mut Il2CppClass,
    pub uint_class: *mut Il2CppClass,
    pub int64_class: *mut Il2CppClass,
    pub uint64_class: *mut Il2CppClass,
    pub single_class: *mut Il2CppClass,
    pub double_class: *mut Il2CppClass,
    pub char_class: *mut Il2CppClass,
    pub string_class: *mut Il2CppClass,
    pub enum_class: *mut Il2CppClass,
    pub array_class: *mut Il2CppClass,
    pub delegate_class: *mut Il2CppClass,
    pub multicastdelegate_class: *mut Il2CppClass,
    pub asyncresult_class: *mut Il2CppClass,
    pub manualresetevent_class: *mut Il2CppClass,
    pub typehandle_class: *mut Il2CppClass,
    pub fieldhandle_class: *mut Il2CppClass,
    pub methodhandle_class: *mut Il2CppClass,
    pub systemtype_class: *mut Il2CppClass,
    pub monotype_class: *mut Il2CppClass,
    pub exception_class: *mut Il2CppClass,
    pub threadabortexception_class: *mut Il2CppClass,
    pub thread_class: *mut Il2CppClass,
    pub internal_thread_class: *mut Il2CppClass,
    pub appdomain_class: *mut Il2CppClass,
    pub appdomain_setup_class: *mut Il2CppClass,
    pub member_info_class: *mut Il2CppClass,
    pub field_info_class: *mut Il2CppClass,
    pub method_info_class: *mut Il2CppClass,
    pub property_info_class: *mut Il2CppClass,
    pub event_info_class: *mut Il2CppClass,
    pub stringbuilder_class: *mut Il2CppClass,
    pub stack_frame_class: *mut Il2CppClass,
    pub stack_trace_class: *mut Il2CppClass,
    pub marshal_class: *mut Il2CppClass,
    pub typed_reference_class: *mut Il2CppClass,
    pub marshalbyrefobject_class: *mut Il2CppClass,
    pub generic_ilist_class: *mut Il2CppClass,
    pub generic_icollection_class: *mut Il2CppClass,
    pub generic_ienumerable_class: *mut Il2CppClass,
    pub generic_ireadonlylist_class: *mut Il2CppClass,
    pub generic_ireadonlycollection_class: *mut Il2CppClass,
    pub runtimetype_class: *mut Il2CppClass,
    pub generic_nullable_class: *mut Il2CppClass,
    pub il2cpp_com_object_class: *mut Il2CppClass,
    pub attribute_class: *mut Il2CppClass,
    pub customattribute_data_class: *mut Il2CppClass,
    pub customattribute_typed_argument_class: *mut Il2CppClass,
    pub customattribute_named_argument_class: *mut Il2CppClass,
    pub version: *mut Il2CppClass,
    pub culture_info: *mut Il2CppClass,
    pub async_call_class: *mut Il2CppClass,
    pub assembly_class: *mut Il2CppClass,
    pub assembly_name_class: *mut Il2CppClass,
    pub parameter_info_class: *mut Il2CppClass,
    pub module_class: *mut Il2CppClass,
    pub system_exception_class: *mut Il2CppClass,
    pub argument_exception_class: *mut Il2CppClass,
    pub wait_handle_class: *mut Il2CppClass,
    pub safe_handle_class: *mut Il2CppClass,
    pub sort_key_class: *mut Il2CppClass,
    pub dbnull_class: *mut Il2CppClass,
    pub error_wrapper_class: *mut Il2CppClass,
    pub missing_class: *mut Il2CppClass,
    pub value_type_class: *mut Il2CppClass,
    pub threadpool_wait_callback_class: *mut Il2CppClass,
    pub threadpool_perform_wait_callback_method: *mut MethodInfo,
    pub mono_method_message_class: *mut Il2CppClass,
    pub ireference_class: *mut Il2CppClass,
    pub ireferencearray_class: *mut Il2CppClass,
    pub ikey_value_pair_class: *mut Il2CppClass,
    pub key_value_pair_class: *mut Il2CppClass,
    pub windows_foundation_uri_class: *mut Il2CppClass,
    pub windows_foundation_iuri_runtime_class_class: *mut Il2CppClass,
    pub system_uri_class: *mut Il2CppClass,
    pub system_guid_class: *mut Il2CppClass,
    pub sbyte_shared_enum: *mut Il2CppClass,
    pub int16_shared_enum: *mut Il2CppClass,
    pub int32_shared_enum: *mut Il2CppClass,
    pub int64_shared_enum: *mut Il2CppClass,
    pub byte_shared_enum: *mut Il2CppClass,
    pub uint16_shared_enum: *mut Il2CppClass,
    pub uint32_shared_enum: *mut Il2CppClass,
    pub uint64_shared_enum: *mut Il2CppClass,
    pub il2cpp_fully_shared_type: *mut Il2CppClass,
    pub il2cpp_fully_shared_struct_type: *mut Il2CppClass,
}
extern "C" {
    pub static mut il2cpp_defaults: Il2CppDefaults;
}
#[repr(C)]
#[derive(Debug)]
pub struct MemberInfo {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Debug)]
pub struct FieldInfo {
    pub name: *const ::std::os::raw::c_char,
    pub type_: *const Il2CppType,
    pub parent: *mut Il2CppClass,
    pub offset: i32,
    pub token: u32,
}
#[repr(C)]
#[derive(Debug)]
pub struct PropertyInfo {
    pub parent: *mut Il2CppClass,
    pub name: *const ::std::os::raw::c_char,
    pub get: *const MethodInfo,
    pub set: *const MethodInfo,
    pub attrs: u32,
    pub token: u32,
}
#[repr(C)]
#[derive(Debug)]
pub struct EventInfo {
    pub name: *const ::std::os::raw::c_char,
    pub eventType: *const Il2CppType,
    pub parent: *mut Il2CppClass,
    pub add: *const MethodInfo,
    pub remove: *const MethodInfo,
    pub raise: *const MethodInfo,
    pub token: u32,
}
pub type InvokerMethod = ::std::option::Option<
    unsafe extern "C" fn(
        arg1: Il2CppMethodPointer,
        arg2: *const MethodInfo,
        arg3: *mut ::std::os::raw::c_void,
        arg4: *mut *mut ::std::os::raw::c_void,
        arg5: *mut ::std::os::raw::c_void,
    ),
>;
pub const MethodVariableKind_kMethodVariableKind_This: MethodVariableKind = 0;
pub const MethodVariableKind_kMethodVariableKind_Parameter: MethodVariableKind = 1;
pub const MethodVariableKind_kMethodVariableKind_LocalVariable: MethodVariableKind = 2;
pub type MethodVariableKind = ::std::os::raw::c_uint;
pub const SequencePointKind_kSequencePointKind_Normal: SequencePointKind = 0;
pub const SequencePointKind_kSequencePointKind_StepOut: SequencePointKind = 1;
pub type SequencePointKind = ::std::os::raw::c_uint;
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppMethodExecutionContextInfo {
    pub typeIndex: TypeIndex,
    pub nameIndex: i32,
    pub scopeIndex: i32,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppMethodExecutionContextInfoIndex {
    pub startIndex: i32,
    pub count: i32,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppMethodScope {
    pub startOffset: i32,
    pub endOffset: i32,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppMethodHeaderInfo {
    pub code_size: i32,
    pub startScope: i32,
    pub numScopes: i32,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppSequencePointSourceFile {
    pub file: *const ::std::os::raw::c_char,
    pub hash: [u8; 16usize],
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppTypeSourceFilePair {
    pub __klassIndex: TypeDefinitionIndex,
    pub sourceFileIndex: i32,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppSequencePoint {
    pub __methodDefinitionIndex: MethodIndex,
    pub sourceFileIndex: i32,
    pub lineStart: i32,
    pub lineEnd: i32,
    pub columnStart: i32,
    pub columnEnd: i32,
    pub ilOffset: i32,
    pub kind: SequencePointKind,
    pub isActive: i32,
    pub id: i32,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppCatchPoint {
    pub __methodDefinitionIndex: MethodIndex,
    pub catchTypeIndex: TypeIndex,
    pub ilOffset: i32,
    pub tryId: i32,
    pub parentTryId: i32,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppDebuggerMetadataRegistration {
    pub methodExecutionContextInfos: *mut Il2CppMethodExecutionContextInfo,
    pub methodExecutionContextInfoIndexes: *mut Il2CppMethodExecutionContextInfoIndex,
    pub methodScopes: *mut Il2CppMethodScope,
    pub methodHeaderInfos: *mut Il2CppMethodHeaderInfo,
    pub sequencePointSourceFiles: *mut Il2CppSequencePointSourceFile,
    pub numSequencePoints: i32,
    pub sequencePoints: *mut Il2CppSequencePoint,
    pub numCatchPoints: i32,
    pub catchPoints: *mut Il2CppCatchPoint,
    pub numTypeSourceFileEntries: i32,
    pub typeSourceFiles: *mut Il2CppTypeSourceFilePair,
    pub methodExecutionContextInfoStrings: *mut *const ::std::os::raw::c_char,
}
#[repr(C)]
pub struct Il2CppRGCTXData {
    pub rgctxDataDummy: __BindgenUnionField<*mut ::std::os::raw::c_void>,
    pub method: __BindgenUnionField<*const MethodInfo>,
    pub type_: __BindgenUnionField<*const Il2CppType>,
    pub klass: __BindgenUnionField<*mut Il2CppClass>,
    pub bindgen_union_field: usize,
}
#[repr(C)]
pub struct MethodInfo {
    pub methodPointer: Il2CppMethodPointer,
    pub virtualMethodPointer: Il2CppMethodPointer,
    pub invoker_method: InvokerMethod,
    pub name: *const ::std::os::raw::c_char,
    pub klass: *mut Il2CppClass,
    pub return_type: *const Il2CppType,
    pub parameters: *mut *const Il2CppType,
    pub __bindgen_anon_1: MethodInfo__bindgen_ty_1,
    pub __bindgen_anon_2: MethodInfo__bindgen_ty_2,
    pub token: u32,
    pub flags: u16,
    pub iflags: u16,
    pub slot: u16,
    pub parameters_count: u8,
    pub _bitfield_align_1: [u8; 0],
    pub _bitfield_1: __BindgenBitfieldUnit<[u8; 1usize]>,
    pub __bindgen_padding_0: u32,
}
#[repr(C)]
pub struct MethodInfo__bindgen_ty_1 {
    pub rgctx_data: __BindgenUnionField<*const Il2CppRGCTXData>,
    pub methodMetadataHandle: __BindgenUnionField<Il2CppMetadataMethodDefinitionHandle>,
    pub bindgen_union_field: usize,
}
#[repr(C)]
pub struct MethodInfo__bindgen_ty_2 {
    pub genericMethod: __BindgenUnionField<*const Il2CppGenericMethod>,
    pub genericContainerHandle: __BindgenUnionField<Il2CppMetadataGenericContainerHandle>,
    pub bindgen_union_field: usize,
}
impl MethodInfo {
    #[inline]
    pub fn is_generic(&self) -> u8 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(0usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_is_generic(&mut self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(0usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub fn is_inflated(&self) -> u8 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(1usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_is_inflated(&mut self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(1usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub fn wrapper_type(&self) -> u8 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(2usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_wrapper_type(&mut self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(2usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub fn has_full_generic_sharing_signature(&self) -> u8 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(3usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_has_full_generic_sharing_signature(&mut self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(3usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub fn new_bitfield_1(
        is_generic: u8,
        is_inflated: u8,
        wrapper_type: u8,
        has_full_generic_sharing_signature: u8,
    ) -> __BindgenBitfieldUnit<[u8; 1usize]> {
        let mut __bindgen_bitfield_unit: __BindgenBitfieldUnit<[u8; 1usize]> = Default::default();
        __bindgen_bitfield_unit.set(0usize, 1u8, {
            let is_generic: u8 = unsafe { ::std::mem::transmute(is_generic) };
            is_generic as u64
        });
        __bindgen_bitfield_unit.set(1usize, 1u8, {
            let is_inflated: u8 = unsafe { ::std::mem::transmute(is_inflated) };
            is_inflated as u64
        });
        __bindgen_bitfield_unit.set(2usize, 1u8, {
            let wrapper_type: u8 = unsafe { ::std::mem::transmute(wrapper_type) };
            wrapper_type as u64
        });
        __bindgen_bitfield_unit.set(3usize, 1u8, {
            let has_full_generic_sharing_signature: u8 =
                unsafe { ::std::mem::transmute(has_full_generic_sharing_signature) };
            has_full_generic_sharing_signature as u64
        });
        __bindgen_bitfield_unit
    }
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppRuntimeInterfaceOffsetPair {
    pub interfaceType: *mut Il2CppClass,
    pub offset: i32,
}
#[repr(C)]
pub struct Il2CppClass {
    pub image: *const Il2CppImage,
    pub gc_desc: *mut ::std::os::raw::c_void,
    pub name: *const ::std::os::raw::c_char,
    pub namespaze: *const ::std::os::raw::c_char,
    pub byval_arg: Il2CppType,
    pub this_arg: Il2CppType,
    pub element_class: *mut Il2CppClass,
    pub castClass: *mut Il2CppClass,
    pub declaringType: *mut Il2CppClass,
    pub parent: *mut Il2CppClass,
    pub generic_class: *mut Il2CppGenericClass,
    pub typeMetadataHandle: Il2CppMetadataTypeHandle,
    pub interopData: *const Il2CppInteropData,
    pub klass: *mut Il2CppClass,
    pub fields: *mut FieldInfo,
    pub events: *const EventInfo,
    pub properties: *const PropertyInfo,
    pub methods: *mut *const MethodInfo,
    pub nestedTypes: *mut *mut Il2CppClass,
    pub implementedInterfaces: *mut *mut Il2CppClass,
    pub interfaceOffsets: *mut Il2CppRuntimeInterfaceOffsetPair,
    pub static_fields: *mut ::std::os::raw::c_void,
    pub rgctx_data: *const Il2CppRGCTXData,
    pub typeHierarchy: *mut *mut Il2CppClass,
    pub unity_user_data: *mut ::std::os::raw::c_void,
    pub initializationExceptionGCHandle: u32,
    pub cctor_started: u32,
    pub cctor_finished_or_no_cctor: u32,
    pub cctor_thread: usize,
    pub genericContainerHandle: Il2CppMetadataGenericContainerHandle,
    pub instance_size: u32,
    pub stack_slot_size: u32,
    pub actualSize: u32,
    pub element_size: u32,
    pub native_size: i32,
    pub static_fields_size: u32,
    pub thread_static_fields_size: u32,
    pub thread_static_fields_offset: i32,
    pub flags: u32,
    pub token: u32,
    pub method_count: u16,
    pub property_count: u16,
    pub field_count: u16,
    pub event_count: u16,
    pub nested_type_count: u16,
    pub vtable_count: u16,
    pub interfaces_count: u16,
    pub interface_offsets_count: u16,
    pub typeHierarchyDepth: u8,
    pub genericRecursionDepth: u8,
    pub rank: u8,
    pub minimumAlignment: u8,
    pub packingSize: u8,
    pub _bitfield_align_1: [u8; 0],
    pub _bitfield_1: __BindgenBitfieldUnit<[u8; 2usize]>,
    pub vtable: __IncompleteArrayField<VirtualInvokeData>,
}
impl Il2CppClass {
    #[inline]
    pub fn initialized_and_no_error(&self) -> u8 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(0usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_initialized_and_no_error(&mut self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(0usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub fn initialized(&self) -> u8 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(1usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_initialized(&mut self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(1usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub fn enumtype(&self) -> u8 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(2usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_enumtype(&mut self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(2usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub fn nullabletype(&self) -> u8 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(3usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_nullabletype(&mut self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(3usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub fn is_generic(&self) -> u8 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(4usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_is_generic(&mut self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(4usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub fn has_references(&self) -> u8 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(5usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_has_references(&mut self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(5usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub fn init_pending(&self) -> u8 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(6usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_init_pending(&mut self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(6usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub fn size_init_pending(&self) -> u8 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(7usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_size_init_pending(&mut self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(7usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub fn size_inited(&self) -> u8 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(8usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_size_inited(&mut self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(8usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub fn has_finalize(&self) -> u8 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(9usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_has_finalize(&mut self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(9usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub fn has_cctor(&self) -> u8 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(10usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_has_cctor(&mut self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(10usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub fn is_blittable(&self) -> u8 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(11usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_is_blittable(&mut self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(11usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub fn is_import_or_windows_runtime(&self) -> u8 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(12usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_is_import_or_windows_runtime(&mut self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(12usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub fn is_vtable_initialized(&self) -> u8 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(13usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_is_vtable_initialized(&mut self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(13usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub fn is_byref_like(&self) -> u8 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(14usize, 1u8) as u8) }
    }
    #[inline]
    pub fn set_is_byref_like(&mut self, val: u8) {
        unsafe {
            let val: u8 = ::std::mem::transmute(val);
            self._bitfield_1.set(14usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub fn new_bitfield_1(
        initialized_and_no_error: u8,
        initialized: u8,
        enumtype: u8,
        nullabletype: u8,
        is_generic: u8,
        has_references: u8,
        init_pending: u8,
        size_init_pending: u8,
        size_inited: u8,
        has_finalize: u8,
        has_cctor: u8,
        is_blittable: u8,
        is_import_or_windows_runtime: u8,
        is_vtable_initialized: u8,
        is_byref_like: u8,
    ) -> __BindgenBitfieldUnit<[u8; 2usize]> {
        let mut __bindgen_bitfield_unit: __BindgenBitfieldUnit<[u8; 2usize]> = Default::default();
        __bindgen_bitfield_unit.set(0usize, 1u8, {
            let initialized_and_no_error: u8 =
                unsafe { ::std::mem::transmute(initialized_and_no_error) };
            initialized_and_no_error as u64
        });
        __bindgen_bitfield_unit.set(1usize, 1u8, {
            let initialized: u8 = unsafe { ::std::mem::transmute(initialized) };
            initialized as u64
        });
        __bindgen_bitfield_unit.set(2usize, 1u8, {
            let enumtype: u8 = unsafe { ::std::mem::transmute(enumtype) };
            enumtype as u64
        });
        __bindgen_bitfield_unit.set(3usize, 1u8, {
            let nullabletype: u8 = unsafe { ::std::mem::transmute(nullabletype) };
            nullabletype as u64
        });
        __bindgen_bitfield_unit.set(4usize, 1u8, {
            let is_generic: u8 = unsafe { ::std::mem::transmute(is_generic) };
            is_generic as u64
        });
        __bindgen_bitfield_unit.set(5usize, 1u8, {
            let has_references: u8 = unsafe { ::std::mem::transmute(has_references) };
            has_references as u64
        });
        __bindgen_bitfield_unit.set(6usize, 1u8, {
            let init_pending: u8 = unsafe { ::std::mem::transmute(init_pending) };
            init_pending as u64
        });
        __bindgen_bitfield_unit.set(7usize, 1u8, {
            let size_init_pending: u8 = unsafe { ::std::mem::transmute(size_init_pending) };
            size_init_pending as u64
        });
        __bindgen_bitfield_unit.set(8usize, 1u8, {
            let size_inited: u8 = unsafe { ::std::mem::transmute(size_inited) };
            size_inited as u64
        });
        __bindgen_bitfield_unit.set(9usize, 1u8, {
            let has_finalize: u8 = unsafe { ::std::mem::transmute(has_finalize) };
            has_finalize as u64
        });
        __bindgen_bitfield_unit.set(10usize, 1u8, {
            let has_cctor: u8 = unsafe { ::std::mem::transmute(has_cctor) };
            has_cctor as u64
        });
        __bindgen_bitfield_unit.set(11usize, 1u8, {
            let is_blittable: u8 = unsafe { ::std::mem::transmute(is_blittable) };
            is_blittable as u64
        });
        __bindgen_bitfield_unit.set(12usize, 1u8, {
            let is_import_or_windows_runtime: u8 =
                unsafe { ::std::mem::transmute(is_import_or_windows_runtime) };
            is_import_or_windows_runtime as u64
        });
        __bindgen_bitfield_unit.set(13usize, 1u8, {
            let is_vtable_initialized: u8 = unsafe { ::std::mem::transmute(is_vtable_initialized) };
            is_vtable_initialized as u64
        });
        __bindgen_bitfield_unit.set(14usize, 1u8, {
            let is_byref_like: u8 = unsafe { ::std::mem::transmute(is_byref_like) };
            is_byref_like as u64
        });
        __bindgen_bitfield_unit
    }
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppTypeDefinitionSizes {
    pub instance_size: u32,
    pub native_size: i32,
    pub static_fields_size: u32,
    pub thread_static_fields_size: u32,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppDomain {
    pub domain: *mut Il2CppAppDomain,
    pub setup: *mut Il2CppAppDomainSetup,
    pub default_context: *mut Il2CppAppContext,
    pub ephemeron_tombstone: *mut Il2CppObject,
    pub friendly_name: *const ::std::os::raw::c_char,
    pub domain_id: u32,
    pub threadpool_jobs: ::std::os::raw::c_int,
    pub agent_info: *mut ::std::os::raw::c_void,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppAssemblyName {
    pub name: *const ::std::os::raw::c_char,
    pub culture: *const ::std::os::raw::c_char,
    pub public_key: *const u8,
    pub hash_alg: u32,
    pub hash_len: i32,
    pub flags: u32,
    pub major: i32,
    pub minor: i32,
    pub build: i32,
    pub revision: i32,
    pub public_key_token: [u8; 8usize],
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppImage {
    pub name: *const ::std::os::raw::c_char,
    pub nameNoExt: *const ::std::os::raw::c_char,
    pub assembly: *mut Il2CppAssembly,
    pub typeCount: u32,
    pub exportedTypeCount: u32,
    pub customAttributeCount: u32,
    pub metadataHandle: Il2CppMetadataImageHandle,
    pub nameToClassHashTable: *mut Il2CppNameToTypeHandleHashTable,
    pub codeGenModule: *const Il2CppCodeGenModule,
    pub token: u32,
    pub dynamic: u8,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppAssembly {
    pub image: *mut Il2CppImage,
    pub token: u32,
    pub referencedAssemblyStart: i32,
    pub referencedAssemblyCount: i32,
    pub aname: Il2CppAssemblyName,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppCodeGenOptions {
    pub enablePrimitiveValueTypeGenericSharing: u8,
    pub maximumRuntimeGenericDepth: ::std::os::raw::c_int,
    pub recursiveGenericIterations: ::std::os::raw::c_int,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppRange {
    pub start: i32,
    pub length: i32,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppTokenRangePair {
    pub token: u32,
    pub range: Il2CppRange,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppTokenIndexMethodTuple {
    pub token: u32,
    pub index: i32,
    pub method: *mut *mut ::std::os::raw::c_void,
    pub __genericMethodIndex: u32,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppTokenAdjustorThunkPair {
    pub token: u32,
    pub adjustorThunk: Il2CppMethodPointer,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppWindowsRuntimeFactoryTableEntry {
    pub type_: *const Il2CppType,
    pub createFactoryFunction: Il2CppMethodPointer,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppCodeGenModule {
    pub moduleName: *const ::std::os::raw::c_char,
    pub methodPointerCount: u32,
    pub methodPointers: *const Il2CppMethodPointer,
    pub adjustorThunkCount: u32,
    pub adjustorThunks: *const Il2CppTokenAdjustorThunkPair,
    pub invokerIndices: *const i32,
    pub reversePInvokeWrapperCount: u32,
    pub reversePInvokeWrapperIndices: *const Il2CppTokenIndexMethodTuple,
    pub rgctxRangesCount: u32,
    pub rgctxRanges: *const Il2CppTokenRangePair,
    pub rgctxsCount: u32,
    pub rgctxs: *const Il2CppRGCTXDefinition,
    pub debuggerMetadata: *const Il2CppDebuggerMetadataRegistration,
    pub moduleInitializer: Il2CppMethodPointer,
    pub staticConstructorTypeIndices: *mut TypeDefinitionIndex,
    pub metadataRegistration: *const Il2CppMetadataRegistration,
    pub codeRegistaration: *const Il2CppCodeRegistration,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppCodeRegistration {
    pub reversePInvokeWrapperCount: u32,
    pub reversePInvokeWrappers: *const Il2CppMethodPointer,
    pub genericMethodPointersCount: u32,
    pub genericMethodPointers: *const Il2CppMethodPointer,
    pub genericAdjustorThunks: *const Il2CppMethodPointer,
    pub invokerPointersCount: u32,
    pub invokerPointers: *const InvokerMethod,
    pub unresolvedIndirectCallCount: u32,
    pub unresolvedVirtualCallPointers: *const Il2CppMethodPointer,
    pub unresolvedInstanceCallPointers: *const Il2CppMethodPointer,
    pub unresolvedStaticCallPointers: *const Il2CppMethodPointer,
    pub interopDataCount: u32,
    pub interopData: *mut Il2CppInteropData,
    pub windowsRuntimeFactoryCount: u32,
    pub windowsRuntimeFactoryTable: *mut Il2CppWindowsRuntimeFactoryTableEntry,
    pub codeGenModulesCount: u32,
    pub codeGenModules: *mut *const Il2CppCodeGenModule,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppMetadataRegistration {
    pub genericClassesCount: i32,
    pub genericClasses: *const *mut Il2CppGenericClass,
    pub genericInstsCount: i32,
    pub genericInsts: *const *const Il2CppGenericInst,
    pub genericMethodTableCount: i32,
    pub genericMethodTable: *const Il2CppGenericMethodFunctionsDefinitions,
    pub typesCount: i32,
    pub types: *const *const Il2CppType,
    pub methodSpecsCount: i32,
    pub methodSpecs: *const Il2CppMethodSpec,
    pub fieldOffsetsCount: FieldIndex,
    pub fieldOffsets: *mut *const i32,
    pub typeDefinitionsSizesCount: TypeDefinitionIndex,
    pub typeDefinitionsSizes: *mut *const Il2CppTypeDefinitionSizes,
    pub metadataUsagesCount: usize,
    pub metadataUsages: *const *mut *mut ::std::os::raw::c_void,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppPerfCounters {
    pub jit_methods: u32,
    pub jit_bytes: u32,
    pub jit_time: u32,
    pub jit_failures: u32,
    pub exceptions_thrown: u32,
    pub exceptions_filters: u32,
    pub exceptions_finallys: u32,
    pub exceptions_depth: u32,
    pub aspnet_requests_queued: u32,
    pub aspnet_requests: u32,
    pub gc_collections0: u32,
    pub gc_collections1: u32,
    pub gc_collections2: u32,
    pub gc_promotions0: u32,
    pub gc_promotions1: u32,
    pub gc_promotion_finalizers: u32,
    pub gc_gen0size: u32,
    pub gc_gen1size: u32,
    pub gc_gen2size: u32,
    pub gc_lossize: u32,
    pub gc_fin_survivors: u32,
    pub gc_num_handles: u32,
    pub gc_allocated: u32,
    pub gc_induced: u32,
    pub gc_time: u32,
    pub gc_total_bytes: u32,
    pub gc_committed_bytes: u32,
    pub gc_reserved_bytes: u32,
    pub gc_num_pinned: u32,
    pub gc_sync_blocks: u32,
    pub remoting_calls: u32,
    pub remoting_channels: u32,
    pub remoting_proxies: u32,
    pub remoting_classes: u32,
    pub remoting_objects: u32,
    pub remoting_contexts: u32,
    pub loader_classes: u32,
    pub loader_total_classes: u32,
    pub loader_appdomains: u32,
    pub loader_total_appdomains: u32,
    pub loader_assemblies: u32,
    pub loader_total_assemblies: u32,
    pub loader_failures: u32,
    pub loader_bytes: u32,
    pub loader_appdomains_uloaded: u32,
    pub thread_contentions: u32,
    pub thread_queue_len: u32,
    pub thread_queue_max: u32,
    pub thread_num_logical: u32,
    pub thread_num_physical: u32,
    pub thread_cur_recognized: u32,
    pub thread_num_recognized: u32,
    pub interop_num_ccw: u32,
    pub interop_num_stubs: u32,
    pub interop_num_marshals: u32,
    pub security_num_checks: u32,
    pub security_num_link_checks: u32,
    pub security_time: u32,
    pub security_depth: u32,
    pub unused: u32,
    pub threadpool_workitems: u64,
    pub threadpool_ioworkitems: u64,
    pub threadpool_threads: ::std::os::raw::c_uint,
    pub threadpool_iothreads: ::std::os::raw::c_uint,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppWaitHandle {
    _unused: [u8; 0],
}
#[repr(C)]
#[derive(Debug)]
pub struct MonitorData {
    _unused: [u8; 0],
}
pub type Il2CppVTable = Il2CppClass;
#[repr(C)]
pub struct Il2CppObject {
    pub __bindgen_anon_1: Il2CppObject__bindgen_ty_1,
    pub monitor: *mut MonitorData,
}
#[repr(C)]
pub struct Il2CppObject__bindgen_ty_1 {
    pub klass: __BindgenUnionField<*mut Il2CppClass>,
    pub vtable: __BindgenUnionField<*mut Il2CppVTable>,
    pub bindgen_union_field: usize,
}
pub type il2cpp_array_lower_bound_t = i32;
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppArrayBounds {
    pub length: il2cpp_array_size_t,
    pub lower_bound: il2cpp_array_lower_bound_t,
}
#[repr(C)]
pub struct Il2CppArray {
    pub obj: Il2CppObject,
    pub bounds: *mut Il2CppArrayBounds,
    pub max_length: il2cpp_array_size_t,
}
#[repr(C)]
pub struct Il2CppArraySize {
    pub obj: Il2CppObject,
    pub bounds: *mut Il2CppArrayBounds,
    pub max_length: il2cpp_array_size_t,
    pub vector: __IncompleteArrayField<*mut ::std::os::raw::c_void>,
}
pub const kIl2CppSizeOfArray: usize = 32;
pub const kIl2CppOffsetOfArrayBounds: usize = 16;
pub const kIl2CppOffsetOfArrayLength: usize = 24;
#[repr(C)]
pub struct Il2CppString {
    pub object: Il2CppObject,
    #[doc = "< Length of string *excluding* the trailing null (which is included in 'chars')."]
    pub length: i32,
    pub chars: __IncompleteArrayField<Il2CppChar>,
}
#[repr(C)]
pub struct Il2CppReflectionType {
    pub object: Il2CppObject,
    pub type_: *const Il2CppType,
}
#[repr(C)]
pub struct Il2CppReflectionRuntimeType {
    pub type_: Il2CppReflectionType,
    pub type_info: *mut Il2CppObject,
    pub genericCache: *mut Il2CppObject,
    pub serializationCtor: *mut Il2CppObject,
}
#[repr(C)]
pub struct Il2CppReflectionMonoType {
    pub type_: Il2CppReflectionRuntimeType,
}
#[repr(C)]
pub struct Il2CppReflectionEvent {
    pub object: Il2CppObject,
    pub cached_add_event: *mut Il2CppObject,
}
#[repr(C)]
pub struct Il2CppReflectionMonoEvent {
    pub event: Il2CppReflectionEvent,
    pub reflectedType: *mut Il2CppReflectionType,
    pub eventInfo: *const EventInfo,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppReflectionMonoEventInfo {
    pub declaringType: *mut Il2CppReflectionType,
    pub reflectedType: *mut Il2CppReflectionType,
    pub name: *mut Il2CppString,
    pub addMethod: *mut Il2CppReflectionMethod,
    pub removeMethod: *mut Il2CppReflectionMethod,
    pub raiseMethod: *mut Il2CppReflectionMethod,
    pub eventAttributes: u32,
    pub otherMethods: *mut Il2CppArray,
}
#[repr(C)]
pub struct Il2CppReflectionField {
    pub object: Il2CppObject,
    pub klass: *mut Il2CppClass,
    pub field: *mut FieldInfo,
    pub name: *mut Il2CppString,
    pub type_: *mut Il2CppReflectionType,
    pub attrs: u32,
}
#[repr(C)]
pub struct Il2CppReflectionProperty {
    pub object: Il2CppObject,
    pub klass: *mut Il2CppClass,
    pub property: *const PropertyInfo,
}
#[repr(C)]
pub struct Il2CppReflectionMethod {
    pub object: Il2CppObject,
    pub method: *const MethodInfo,
    pub name: *mut Il2CppString,
    pub reftype: *mut Il2CppReflectionType,
}
#[repr(C)]
pub struct Il2CppReflectionGenericMethod {
    pub base: Il2CppReflectionMethod,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppMethodInfo {
    pub parent: *mut Il2CppReflectionType,
    pub ret: *mut Il2CppReflectionType,
    pub attrs: u32,
    pub implattrs: u32,
    pub callconv: u32,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppPropertyInfo {
    pub parent: *mut Il2CppReflectionType,
    pub declaringType: *mut Il2CppReflectionType,
    pub name: *mut Il2CppString,
    pub get: *mut Il2CppReflectionMethod,
    pub set: *mut Il2CppReflectionMethod,
    pub attrs: u32,
}
#[repr(C)]
pub struct Il2CppReflectionParameter {
    pub object: Il2CppObject,
    pub AttrsImpl: u32,
    pub ClassImpl: *mut Il2CppReflectionType,
    pub DefaultValueImpl: *mut Il2CppObject,
    pub MemberImpl: *mut Il2CppObject,
    pub NameImpl: *mut Il2CppString,
    pub PositionImpl: i32,
    pub MarshalAs: *mut Il2CppObject,
}
#[repr(C)]
pub struct Il2CppReflectionModule {
    pub obj: Il2CppObject,
    pub image: *const Il2CppImage,
    pub assembly: *mut Il2CppReflectionAssembly,
    pub fqname: *mut Il2CppString,
    pub name: *mut Il2CppString,
    pub scopename: *mut Il2CppString,
    pub is_resource: u8,
    pub token: u32,
}
#[repr(C)]
pub struct Il2CppReflectionAssemblyName {
    pub obj: Il2CppObject,
    pub name: *mut Il2CppString,
    pub codebase: *mut Il2CppString,
    pub major: i32,
    pub minor: i32,
    pub build: i32,
    pub revision: i32,
    pub cultureInfo: *mut Il2CppObject,
    pub flags: u32,
    pub hashalg: u32,
    pub keypair: *mut Il2CppObject,
    pub publicKey: *mut Il2CppArray,
    pub keyToken: *mut Il2CppArray,
    pub versioncompat: u32,
    pub version: *mut Il2CppObject,
    pub processor_architecture: u32,
    pub contentType: u32,
}
#[repr(C)]
pub struct Il2CppReflectionAssembly {
    pub object: Il2CppObject,
    pub assembly: *const Il2CppAssembly,
    pub evidence: *mut Il2CppObject,
    pub resolve_event_holder: *mut Il2CppObject,
    pub minimum: *mut Il2CppObject,
    pub optional: *mut Il2CppObject,
    pub refuse: *mut Il2CppObject,
    pub granted: *mut Il2CppObject,
    pub denied: *mut Il2CppObject,
    pub from_byte_array: u8,
    pub name: *mut Il2CppString,
}
#[repr(C)]
pub struct Il2CppReflectionMarshal {
    pub object: Il2CppObject,
    pub count: i32,
    pub type_: i32,
    pub eltype: i32,
    pub guid: *mut Il2CppString,
    pub mcookie: *mut Il2CppString,
    pub marshaltype: *mut Il2CppString,
    pub marshaltyperef: *mut Il2CppObject,
    pub param_num: i32,
    pub has_size: u8,
}
#[repr(C)]
pub struct Il2CppReflectionPointer {
    pub object: Il2CppObject,
    pub data: *mut ::std::os::raw::c_void,
    pub type_: *mut Il2CppReflectionType,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppThreadName {
    pub chars: *mut Il2CppChar,
    pub unused: i32,
    pub length: i32,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppRefCount {
    pub ref_: u32,
    pub destructor: ::std::option::Option<unsafe extern "C" fn(data: *mut ::std::os::raw::c_void)>,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppLongLivedThreadData {
    pub ref_: Il2CppRefCount,
    pub synch_cs: *mut ::std::os::raw::c_void,
}
#[repr(C)]
pub struct Il2CppInternalThread {
    pub obj: Il2CppObject,
    pub lock_thread_id: ::std::os::raw::c_int,
    pub handle: *mut ::std::os::raw::c_void,
    pub native_handle: *mut ::std::os::raw::c_void,
    pub name: Il2CppThreadName,
    pub state: u32,
    pub abort_exc: *mut Il2CppObject,
    pub abort_state_handle: ::std::os::raw::c_int,
    pub tid: u64,
    pub debugger_thread: isize,
    pub static_data: *mut ::std::os::raw::c_void,
    pub runtime_thread_info: *mut ::std::os::raw::c_void,
    pub current_appcontext: *mut Il2CppObject,
    pub root_domain_thread: *mut Il2CppObject,
    pub _serialized_principal: *mut Il2CppArray,
    pub _serialized_principal_version: ::std::os::raw::c_int,
    pub appdomain_refs: *mut ::std::os::raw::c_void,
    pub interruption_requested: i32,
    pub longlived: *mut ::std::os::raw::c_void,
    pub threadpool_thread: u8,
    pub thread_interrupt_requested: u8,
    pub stack_size: ::std::os::raw::c_int,
    pub apartment_state: u8,
    pub critical_region_level: ::std::os::raw::c_int,
    pub managed_id: ::std::os::raw::c_int,
    pub small_id: u32,
    pub manage_callback: *mut ::std::os::raw::c_void,
    pub flags: isize,
    pub thread_pinning_ref: *mut ::std::os::raw::c_void,
    pub abort_protected_block_count: *mut ::std::os::raw::c_void,
    pub priority: i32,
    pub owned_mutexes: *mut ::std::os::raw::c_void,
    pub suspended: *mut ::std::os::raw::c_void,
    pub self_suspended: i32,
    pub thread_state: usize,
    pub unused: [*mut ::std::os::raw::c_void; 3usize],
    pub last: *mut ::std::os::raw::c_void,
}
#[repr(C)]
pub struct Il2CppIOSelectorJob {
    pub object: Il2CppObject,
    pub operation: i32,
    pub callback: *mut Il2CppObject,
    pub state: *mut Il2CppObject,
}
pub const Il2CppCallType_Il2Cpp_CallType_Sync: Il2CppCallType = 0;
pub const Il2CppCallType_Il2Cpp_CallType_BeginInvoke: Il2CppCallType = 1;
pub const Il2CppCallType_Il2Cpp_CallType_EndInvoke: Il2CppCallType = 2;
pub const Il2CppCallType_Il2Cpp_CallType_OneWay: Il2CppCallType = 3;
pub type Il2CppCallType = ::std::os::raw::c_uint;
#[repr(C)]
pub struct Il2CppMethodMessage {
    pub obj: Il2CppObject,
    pub method: *mut Il2CppReflectionMethod,
    pub args: *mut Il2CppArray,
    pub names: *mut Il2CppArray,
    pub arg_types: *mut Il2CppArray,
    pub ctx: *mut Il2CppObject,
    pub rval: *mut Il2CppObject,
    pub exc: *mut Il2CppObject,
    pub async_result: *mut Il2CppAsyncResult,
    pub call_type: u32,
}
#[repr(C)]
pub struct Il2CppAppDomainSetup {
    pub object: Il2CppObject,
    pub application_base: *mut Il2CppString,
    pub application_name: *mut Il2CppString,
    pub cache_path: *mut Il2CppString,
    pub configuration_file: *mut Il2CppString,
    pub dynamic_base: *mut Il2CppString,
    pub license_file: *mut Il2CppString,
    pub private_bin_path: *mut Il2CppString,
    pub private_bin_path_probe: *mut Il2CppString,
    pub shadow_copy_directories: *mut Il2CppString,
    pub shadow_copy_files: *mut Il2CppString,
    pub publisher_policy: u8,
    pub path_changed: u8,
    pub loader_optimization: ::std::os::raw::c_int,
    pub disallow_binding_redirects: u8,
    pub disallow_code_downloads: u8,
    pub activation_arguments: *mut Il2CppObject,
    pub domain_initializer: *mut Il2CppObject,
    pub application_trust: *mut Il2CppObject,
    pub domain_initializer_args: *mut Il2CppArray,
    pub disallow_appbase_probe: u8,
    pub configuration_bytes: *mut Il2CppArray,
    pub serialized_non_primitives: *mut Il2CppArray,
}
#[repr(C)]
pub struct Il2CppThread {
    pub obj: Il2CppObject,
    pub internal_thread: *mut Il2CppInternalThread,
    pub start_obj: *mut Il2CppObject,
    pub pending_exception: *mut Il2CppException,
    pub principal: *mut Il2CppObject,
    pub principal_version: i32,
    pub delegate: *mut Il2CppDelegate,
    pub executionContext: *mut Il2CppObject,
    pub executionContextBelongsToOuterScope: u8,
}
#[repr(C)]
pub struct Il2CppException {
    pub object: Il2CppObject,
    pub className: *mut Il2CppString,
    pub message: *mut Il2CppString,
    pub _data: *mut Il2CppObject,
    pub inner_ex: *mut Il2CppException,
    pub _helpURL: *mut Il2CppString,
    pub trace_ips: *mut Il2CppArray,
    pub stack_trace: *mut Il2CppString,
    pub remote_stack_trace: *mut Il2CppString,
    pub remote_stack_index: ::std::os::raw::c_int,
    pub _dynamicMethods: *mut Il2CppObject,
    pub hresult: il2cpp_hresult_t,
    pub source: *mut Il2CppString,
    pub safeSerializationManager: *mut Il2CppObject,
    pub captured_traces: *mut Il2CppArray,
    pub native_trace_ips: *mut Il2CppArray,
    pub caught_in_unmanaged: i32,
}
#[repr(C)]
pub struct Il2CppSystemException {
    pub base: Il2CppException,
}
#[repr(C)]
pub struct Il2CppArgumentException {
    pub base: Il2CppException,
    pub argName: *mut Il2CppString,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppTypedRef {
    pub type_: *const Il2CppType,
    pub value: *mut ::std::os::raw::c_void,
    pub klass: *mut Il2CppClass,
}
#[repr(C)]
pub struct Il2CppDelegate {
    pub object: Il2CppObject,
    pub method_ptr: Il2CppMethodPointer,
    pub invoke_impl: Il2CppMethodPointer,
    pub target: *mut Il2CppObject,
    pub method: *const MethodInfo,
    pub delegate_trampoline: *mut ::std::os::raw::c_void,
    pub extraArg: isize,
    pub invoke_impl_this: *mut Il2CppObject,
    pub interp_method: *mut ::std::os::raw::c_void,
    pub interp_invoke_impl: *mut ::std::os::raw::c_void,
    pub method_info: *mut Il2CppReflectionMethod,
    pub original_method_info: *mut Il2CppReflectionMethod,
    pub data: *mut Il2CppObject,
    pub method_is_virtual: u8,
}
#[repr(C)]
pub struct Il2CppMulticastDelegate {
    pub delegate: Il2CppDelegate,
    pub delegates: *mut Il2CppArray,
}
#[repr(C)]
pub struct Il2CppMarshalByRefObject {
    pub obj: Il2CppObject,
    pub identity: *mut Il2CppObject,
}
pub type Il2CppFullySharedGenericAny = *mut ::std::os::raw::c_void;
pub type Il2CppFullySharedGenericStruct = *mut ::std::os::raw::c_void;
#[repr(C)]
pub struct Il2CppAppDomain {
    pub mbr: Il2CppMarshalByRefObject,
    pub data: *mut Il2CppDomain,
}
#[repr(C)]
pub struct Il2CppStackFrame {
    pub obj: Il2CppObject,
    pub il_offset: i32,
    pub native_offset: i32,
    pub methodAddress: u64,
    pub methodIndex: u32,
    pub method: *mut Il2CppReflectionMethod,
    pub filename: *mut Il2CppString,
    pub line: i32,
    pub column: i32,
    pub internal_method_name: *mut Il2CppString,
}
#[repr(C)]
pub struct Il2CppDateTimeFormatInfo {
    pub obj: Il2CppObject,
    pub CultureData: *mut Il2CppObject,
    pub Name: *mut Il2CppString,
    pub LangName: *mut Il2CppString,
    pub CompareInfo: *mut Il2CppObject,
    pub CultureInfo: *mut Il2CppObject,
    pub AMDesignator: *mut Il2CppString,
    pub PMDesignator: *mut Il2CppString,
    pub DateSeparator: *mut Il2CppString,
    pub GeneralShortTimePattern: *mut Il2CppString,
    pub GeneralLongTimePattern: *mut Il2CppString,
    pub TimeSeparator: *mut Il2CppString,
    pub MonthDayPattern: *mut Il2CppString,
    pub DateTimeOffsetPattern: *mut Il2CppString,
    pub Calendar: *mut Il2CppObject,
    pub FirstDayOfWeek: u32,
    pub CalendarWeekRule: u32,
    pub FullDateTimePattern: *mut Il2CppString,
    pub AbbreviatedDayNames: *mut Il2CppArray,
    pub ShortDayNames: *mut Il2CppArray,
    pub DayNames: *mut Il2CppArray,
    pub AbbreviatedMonthNames: *mut Il2CppArray,
    pub MonthNames: *mut Il2CppArray,
    pub GenitiveMonthNames: *mut Il2CppArray,
    pub GenitiveAbbreviatedMonthNames: *mut Il2CppArray,
    pub LeapYearMonthNames: *mut Il2CppArray,
    pub LongDatePattern: *mut Il2CppString,
    pub ShortDatePattern: *mut Il2CppString,
    pub YearMonthPattern: *mut Il2CppString,
    pub LongTimePattern: *mut Il2CppString,
    pub ShortTimePattern: *mut Il2CppString,
    pub YearMonthPatterns: *mut Il2CppArray,
    pub ShortDatePatterns: *mut Il2CppArray,
    pub LongDatePatterns: *mut Il2CppArray,
    pub ShortTimePatterns: *mut Il2CppArray,
    pub LongTimePatterns: *mut Il2CppArray,
    pub EraNames: *mut Il2CppArray,
    pub AbbrevEraNames: *mut Il2CppArray,
    pub AbbrevEnglishEraNames: *mut Il2CppArray,
    pub OptionalCalendars: *mut Il2CppArray,
    pub readOnly: u8,
    pub FormatFlags: i32,
    pub CultureID: i32,
    pub UseUserOverride: u8,
    pub UseCalendarInfo: u8,
    pub DataItem: i32,
    pub IsDefaultCalendar: u8,
    pub DateWords: *mut Il2CppArray,
    pub FullTimeSpanPositivePattern: *mut Il2CppString,
    pub FullTimeSpanNegativePattern: *mut Il2CppString,
    pub dtfiTokenHash: *mut Il2CppArray,
}
#[repr(C)]
pub struct Il2CppNumberFormatInfo {
    pub obj: Il2CppObject,
    pub numberGroupSizes: *mut Il2CppArray,
    pub currencyGroupSizes: *mut Il2CppArray,
    pub percentGroupSizes: *mut Il2CppArray,
    pub positiveSign: *mut Il2CppString,
    pub negativeSign: *mut Il2CppString,
    pub numberDecimalSeparator: *mut Il2CppString,
    pub numberGroupSeparator: *mut Il2CppString,
    pub currencyGroupSeparator: *mut Il2CppString,
    pub currencyDecimalSeparator: *mut Il2CppString,
    pub currencySymbol: *mut Il2CppString,
    pub ansiCurrencySymbol: *mut Il2CppString,
    pub naNSymbol: *mut Il2CppString,
    pub positiveInfinitySymbol: *mut Il2CppString,
    pub negativeInfinitySymbol: *mut Il2CppString,
    pub percentDecimalSeparator: *mut Il2CppString,
    pub percentGroupSeparator: *mut Il2CppString,
    pub percentSymbol: *mut Il2CppString,
    pub perMilleSymbol: *mut Il2CppString,
    pub nativeDigits: *mut Il2CppArray,
    pub dataItem: ::std::os::raw::c_int,
    pub numberDecimalDigits: ::std::os::raw::c_int,
    pub currencyDecimalDigits: ::std::os::raw::c_int,
    pub currencyPositivePattern: ::std::os::raw::c_int,
    pub currencyNegativePattern: ::std::os::raw::c_int,
    pub numberNegativePattern: ::std::os::raw::c_int,
    pub percentPositivePattern: ::std::os::raw::c_int,
    pub percentNegativePattern: ::std::os::raw::c_int,
    pub percentDecimalDigits: ::std::os::raw::c_int,
    pub digitSubstitution: ::std::os::raw::c_int,
    pub readOnly: u8,
    pub useUserOverride: u8,
    pub isInvariant: u8,
    pub validForParseAsNumber: u8,
    pub validForParseAsCurrency: u8,
}
#[repr(C)]
#[derive(Debug)]
pub struct NumberFormatEntryManaged {
    pub currency_decimal_digits: i32,
    pub currency_decimal_separator: i32,
    pub currency_group_separator: i32,
    pub currency_group_sizes0: i32,
    pub currency_group_sizes1: i32,
    pub currency_negative_pattern: i32,
    pub currency_positive_pattern: i32,
    pub currency_symbol: i32,
    pub nan_symbol: i32,
    pub negative_infinity_symbol: i32,
    pub negative_sign: i32,
    pub number_decimal_digits: i32,
    pub number_decimal_separator: i32,
    pub number_group_separator: i32,
    pub number_group_sizes0: i32,
    pub number_group_sizes1: i32,
    pub number_negative_pattern: i32,
    pub per_mille_symbol: i32,
    pub percent_negative_pattern: i32,
    pub percent_positive_pattern: i32,
    pub percent_symbol: i32,
    pub positive_infinity_symbol: i32,
    pub positive_sign: i32,
}
#[repr(C)]
pub struct Il2CppCultureData {
    pub obj: Il2CppObject,
    pub AMDesignator: *mut Il2CppString,
    pub PMDesignator: *mut Il2CppString,
    pub TimeSeparator: *mut Il2CppString,
    pub LongTimePatterns: *mut Il2CppArray,
    pub ShortTimePatterns: *mut Il2CppArray,
    pub FirstDayOfWeek: u32,
    pub CalendarWeekRule: u32,
}
#[repr(C)]
pub struct Il2CppCalendarData {
    pub obj: Il2CppObject,
    pub NativeName: *mut Il2CppString,
    pub ShortDatePatterns: *mut Il2CppArray,
    pub YearMonthPatterns: *mut Il2CppArray,
    pub LongDatePatterns: *mut Il2CppArray,
    pub MonthDayPattern: *mut Il2CppString,
    pub EraNames: *mut Il2CppArray,
    pub AbbreviatedEraNames: *mut Il2CppArray,
    pub AbbreviatedEnglishEraNames: *mut Il2CppArray,
    pub DayNames: *mut Il2CppArray,
    pub AbbreviatedDayNames: *mut Il2CppArray,
    pub SuperShortDayNames: *mut Il2CppArray,
    pub MonthNames: *mut Il2CppArray,
    pub AbbreviatedMonthNames: *mut Il2CppArray,
    pub GenitiveMonthNames: *mut Il2CppArray,
    pub GenitiveAbbreviatedMonthNames: *mut Il2CppArray,
}
#[repr(C)]
pub struct Il2CppCultureInfo {
    pub obj: Il2CppObject,
    pub is_read_only: u8,
    pub lcid: i32,
    pub parent_lcid: i32,
    pub datetime_index: i32,
    pub number_index: i32,
    pub default_calendar_type: i32,
    pub use_user_override: u8,
    pub number_format: *mut Il2CppNumberFormatInfo,
    pub datetime_format: *mut Il2CppDateTimeFormatInfo,
    pub textinfo: *mut Il2CppObject,
    pub name: *mut Il2CppString,
    pub englishname: *mut Il2CppString,
    pub nativename: *mut Il2CppString,
    pub iso3lang: *mut Il2CppString,
    pub iso2lang: *mut Il2CppString,
    pub win3lang: *mut Il2CppString,
    pub territory: *mut Il2CppString,
    pub native_calendar_names: *mut Il2CppArray,
    pub compareinfo: *mut Il2CppString,
    pub text_info_data: *const ::std::os::raw::c_void,
    pub dataItem: ::std::os::raw::c_int,
    pub calendar: *mut Il2CppObject,
    pub parent_culture: *mut Il2CppObject,
    pub constructed: u8,
    pub cached_serialized_form: *mut Il2CppArray,
    pub cultureData: *mut Il2CppObject,
    pub isInherited: u8,
}
#[repr(C)]
pub struct Il2CppRegionInfo {
    pub obj: Il2CppObject,
    pub geo_id: i32,
    pub iso2name: *mut Il2CppString,
    pub iso3name: *mut Il2CppString,
    pub win3name: *mut Il2CppString,
    pub english_name: *mut Il2CppString,
    pub native_name: *mut Il2CppString,
    pub currency_symbol: *mut Il2CppString,
    pub iso_currency_symbol: *mut Il2CppString,
    pub currency_english_name: *mut Il2CppString,
    pub currency_native_name: *mut Il2CppString,
}
#[repr(C)]
pub struct Il2CppSafeHandle {
    pub base: Il2CppObject,
    pub handle: *mut ::std::os::raw::c_void,
    pub state: ::std::os::raw::c_int,
    pub owns_handle: u8,
    pub fullyInitialized: u8,
}
#[repr(C)]
pub struct Il2CppStringBuilder {
    pub object: Il2CppObject,
    pub chunkChars: *mut Il2CppArray,
    pub chunkPrevious: *mut Il2CppStringBuilder,
    pub chunkLength: ::std::os::raw::c_int,
    pub chunkOffset: ::std::os::raw::c_int,
    pub maxCapacity: ::std::os::raw::c_int,
}
#[repr(C)]
pub struct Il2CppSocketAddress {
    pub base: Il2CppObject,
    pub m_Size: ::std::os::raw::c_int,
    pub data: *mut Il2CppArray,
    pub m_changed: u8,
    pub m_hash: ::std::os::raw::c_int,
}
#[repr(C)]
pub struct Il2CppSortKey {
    pub base: Il2CppObject,
    pub str_: *mut Il2CppString,
    pub key: *mut Il2CppArray,
    pub options: i32,
    pub lcid: i32,
}
#[repr(C)]
pub struct Il2CppErrorWrapper {
    pub base: Il2CppObject,
    pub errorCode: i32,
}
#[repr(C)]
pub struct Il2CppAsyncResult {
    pub base: Il2CppObject,
    pub async_state: *mut Il2CppObject,
    pub handle: *mut Il2CppWaitHandle,
    pub async_delegate: *mut Il2CppDelegate,
    pub data: *mut ::std::os::raw::c_void,
    pub object_data: *mut Il2CppAsyncCall,
    pub sync_completed: u8,
    pub completed: u8,
    pub endinvoke_called: u8,
    pub async_callback: *mut Il2CppObject,
    pub execution_context: *mut Il2CppObject,
    pub original_context: *mut Il2CppObject,
}
#[repr(C)]
pub struct Il2CppAsyncCall {
    pub base: Il2CppObject,
    pub msg: *mut Il2CppMethodMessage,
    pub cb_method: *mut MethodInfo,
    pub cb_target: *mut Il2CppDelegate,
    pub state: *mut Il2CppObject,
    pub res: *mut Il2CppObject,
    pub out_args: *mut Il2CppArray,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppExceptionWrapper {
    pub ex: *mut Il2CppException,
}
#[repr(C)]
pub struct Il2CppIOAsyncResult {
    pub base: Il2CppObject,
    pub callback: *mut Il2CppDelegate,
    pub state: *mut Il2CppObject,
    pub wait_handle: *mut Il2CppWaitHandle,
    pub completed_synchronously: u8,
    pub completed: u8,
}
#[doc = " Corresponds to Mono's internal System.Net.Sockets.Socket.SocketAsyncResult\n class. Has no relation to Il2CppAsyncResult."]
#[repr(C)]
pub struct Il2CppSocketAsyncResult {
    pub base: Il2CppIOAsyncResult,
    pub socket: *mut Il2CppObject,
    pub operation: i32,
    pub delayedException: *mut Il2CppException,
    pub endPoint: *mut Il2CppObject,
    pub buffer: *mut Il2CppArray,
    pub offset: i32,
    pub size: i32,
    pub socket_flags: i32,
    pub acceptSocket: *mut Il2CppObject,
    pub addresses: *mut Il2CppArray,
    pub port: i32,
    pub buffers: *mut Il2CppObject,
    pub reuseSocket: u8,
    pub currentAddress: i32,
    pub acceptedSocket: *mut Il2CppObject,
    pub total: i32,
    pub error: i32,
    pub endCalled: i32,
}
pub const Il2CppResourceLocation_IL2CPP_RESOURCE_LOCATION_EMBEDDED: Il2CppResourceLocation = 1;
pub const Il2CppResourceLocation_IL2CPP_RESOURCE_LOCATION_ANOTHER_ASSEMBLY: Il2CppResourceLocation =
    2;
pub const Il2CppResourceLocation_IL2CPP_RESOURCE_LOCATION_IN_MANIFEST: Il2CppResourceLocation = 4;
pub type Il2CppResourceLocation = ::std::os::raw::c_uint;
#[repr(C)]
pub struct Il2CppManifestResourceInfo {
    pub object: Il2CppObject,
    pub assembly: *mut Il2CppReflectionAssembly,
    pub filename: *mut Il2CppString,
    pub location: u32,
}
#[repr(C)]
pub struct Il2CppAppContext {
    pub obj: Il2CppObject,
    pub domain_id: i32,
    pub context_id: i32,
    pub static_data: *mut ::std::os::raw::c_void,
}
#[repr(C)]
pub struct Il2CppDecimal {
    pub reserved: u16,
    pub u: Il2CppDecimal__bindgen_ty_1,
    pub Hi32: u32,
    pub v: Il2CppDecimal__bindgen_ty_2,
}
#[repr(C)]
pub struct Il2CppDecimal__bindgen_ty_1 {
    pub u: __BindgenUnionField<Il2CppDecimal__bindgen_ty_1__bindgen_ty_1>,
    pub signscale: __BindgenUnionField<u16>,
    pub bindgen_union_field: u16,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppDecimal__bindgen_ty_1__bindgen_ty_1 {
    pub scale: u8,
    pub sign: u8,
}
#[repr(C)]
pub struct Il2CppDecimal__bindgen_ty_2 {
    pub v: __BindgenUnionField<Il2CppDecimal__bindgen_ty_2__bindgen_ty_1>,
    pub Lo64: __BindgenUnionField<u64>,
    pub bindgen_union_field: u64,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppDecimal__bindgen_ty_2__bindgen_ty_1 {
    pub Lo32: u32,
    pub Mid32: u32,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppDouble {
    pub _bitfield_align_1: [u32; 0],
    pub _bitfield_1: __BindgenBitfieldUnit<[u8; 8usize]>,
}
impl Il2CppDouble {
    #[inline]
    pub fn mantLo(&self) -> u32 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(0usize, 32u8) as u32) }
    }
    #[inline]
    pub fn set_mantLo(&mut self, val: u32) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            self._bitfield_1.set(0usize, 32u8, val as u64)
        }
    }
    #[inline]
    pub fn mantHi(&self) -> u32 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(32usize, 20u8) as u32) }
    }
    #[inline]
    pub fn set_mantHi(&mut self, val: u32) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            self._bitfield_1.set(32usize, 20u8, val as u64)
        }
    }
    #[inline]
    pub fn exp(&self) -> u32 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(52usize, 11u8) as u32) }
    }
    #[inline]
    pub fn set_exp(&mut self, val: u32) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            self._bitfield_1.set(52usize, 11u8, val as u64)
        }
    }
    #[inline]
    pub fn sign(&self) -> u32 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(63usize, 1u8) as u32) }
    }
    #[inline]
    pub fn set_sign(&mut self, val: u32) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            self._bitfield_1.set(63usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub fn new_bitfield_1(
        mantLo: u32,
        mantHi: u32,
        exp: u32,
        sign: u32,
    ) -> __BindgenBitfieldUnit<[u8; 8usize]> {
        let mut __bindgen_bitfield_unit: __BindgenBitfieldUnit<[u8; 8usize]> = Default::default();
        __bindgen_bitfield_unit.set(0usize, 32u8, {
            let mantLo: u32 = unsafe { ::std::mem::transmute(mantLo) };
            mantLo as u64
        });
        __bindgen_bitfield_unit.set(32usize, 20u8, {
            let mantHi: u32 = unsafe { ::std::mem::transmute(mantHi) };
            mantHi as u64
        });
        __bindgen_bitfield_unit.set(52usize, 11u8, {
            let exp: u32 = unsafe { ::std::mem::transmute(exp) };
            exp as u64
        });
        __bindgen_bitfield_unit.set(63usize, 1u8, {
            let sign: u32 = unsafe { ::std::mem::transmute(sign) };
            sign as u64
        });
        __bindgen_bitfield_unit
    }
}
#[repr(C)]
pub struct Il2CppDouble_double {
    pub s: __BindgenUnionField<Il2CppDouble>,
    pub d: __BindgenUnionField<f64>,
    pub bindgen_union_field: u64,
}
pub const Il2CppDecimalCompareResult_IL2CPP_DECIMAL_CMP_LT: Il2CppDecimalCompareResult = -1;
pub const Il2CppDecimalCompareResult_IL2CPP_DECIMAL_CMP_EQ: Il2CppDecimalCompareResult = 0;
pub const Il2CppDecimalCompareResult_IL2CPP_DECIMAL_CMP_GT: Il2CppDecimalCompareResult = 1;
pub type Il2CppDecimalCompareResult = ::std::os::raw::c_int;
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppSingle {
    pub _bitfield_align_1: [u32; 0],
    pub _bitfield_1: __BindgenBitfieldUnit<[u8; 4usize]>,
}
impl Il2CppSingle {
    #[inline]
    pub fn mant(&self) -> u32 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(0usize, 23u8) as u32) }
    }
    #[inline]
    pub fn set_mant(&mut self, val: u32) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            self._bitfield_1.set(0usize, 23u8, val as u64)
        }
    }
    #[inline]
    pub fn exp(&self) -> u32 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(23usize, 8u8) as u32) }
    }
    #[inline]
    pub fn set_exp(&mut self, val: u32) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            self._bitfield_1.set(23usize, 8u8, val as u64)
        }
    }
    #[inline]
    pub fn sign(&self) -> u32 {
        unsafe { ::std::mem::transmute(self._bitfield_1.get(31usize, 1u8) as u32) }
    }
    #[inline]
    pub fn set_sign(&mut self, val: u32) {
        unsafe {
            let val: u32 = ::std::mem::transmute(val);
            self._bitfield_1.set(31usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub fn new_bitfield_1(mant: u32, exp: u32, sign: u32) -> __BindgenBitfieldUnit<[u8; 4usize]> {
        let mut __bindgen_bitfield_unit: __BindgenBitfieldUnit<[u8; 4usize]> = Default::default();
        __bindgen_bitfield_unit.set(0usize, 23u8, {
            let mant: u32 = unsafe { ::std::mem::transmute(mant) };
            mant as u64
        });
        __bindgen_bitfield_unit.set(23usize, 8u8, {
            let exp: u32 = unsafe { ::std::mem::transmute(exp) };
            exp as u64
        });
        __bindgen_bitfield_unit.set(31usize, 1u8, {
            let sign: u32 = unsafe { ::std::mem::transmute(sign) };
            sign as u64
        });
        __bindgen_bitfield_unit
    }
}
#[repr(C)]
pub struct Il2CppSingle_float {
    pub s: __BindgenUnionField<Il2CppSingle>,
    pub f: __BindgenUnionField<f32>,
    pub bindgen_union_field: u32,
}
#[repr(C)]
#[derive(Debug)]
pub struct Il2CppByReference {
    pub value: isize,
}



/************** Unity + Android-specific structs **************/

pub const ScreenOrientation_Unknown: ScreenOrientation = 0;
pub const ScreenOrientation_Portrait: ScreenOrientation = 1;
pub const ScreenOrientation_PortraitUpsideDown: ScreenOrientation = 2;
pub const ScreenOrientation_LandscapeLeft: ScreenOrientation = 3;
pub const ScreenOrientation_LandscapeRight: ScreenOrientation = 4;
pub const ScreenOrientation_AutoRotation: ScreenOrientation = 5;
pub const ScreenOrientation_Landscape: ScreenOrientation = 3;
pub type ScreenOrientation = i32;
#[repr(C)]
#[derive(Debug)]
pub struct Color_t {
    pub r: f32,
    pub g: f32,
    pub b: f32,
    pub a: f32,
}
#[repr(C)]
#[derive(Debug)]
pub struct Color32_t {
    pub r: u8,
    pub g: u8,
    pub b: u8,
    pub a: u8,
}
impl Color32_t {
    pub fn as_slice(&self) -> &[u8] {
        unsafe { std::slice::from_raw_parts(self as *const Color32_t as _, 4) }
    }

    pub fn as_mut_slice(&mut self) -> &[u8] {
        unsafe { std::slice::from_raw_parts_mut(self as *mut Color32_t as _, 4) }
    }
}
#[repr(C)]
#[derive(Debug, Default)]
pub struct Vector2_t {
    pub x: f32,
    pub y: f32,
}
#[repr(C)]
#[derive(Debug)]
pub struct Vector2Int_t {
    pub x: i32,
    pub y: i32
}
impl std::ops::MulAssign<f32> for Vector2Int_t {
    fn mul_assign(&mut self, rhs: f32) {
        self.x = (self.x as f32 * rhs) as i32;
        self.y = (self.y as f32 * rhs) as i32;
    }
}
#[repr(C)]
#[derive(Debug, Deserialize)]
pub struct Vector3_t {
    pub x: f32,
    pub y: f32,
    pub z: f32,
}
#[repr(C)]
#[derive(Debug)]
pub struct Rect_t {
    pub x: f32,
    pub y: f32,
    pub width: f32,
    pub height: f32,
}
#[repr(C)]
#[derive(Debug)]
pub struct Quaternion_t {
    pub w: f32,
    pub x: f32,
    pub y: f32,
    pub z: f32,
}
pub const HorizontalWrapMode_Wrap: HorizontalWrapMode = 0;
pub const HorizontalWrapMode_Overflow: HorizontalWrapMode = 1;
pub type HorizontalWrapMode = ::std::os::raw::c_int;
#[repr(C)]
#[derive(Debug)]
pub struct TextGenerationSettings_t {
    pub font: *mut ::std::os::raw::c_void,
    pub color: Color_t,
    pub fontSize: i32,
    pub lineSpacing: f32,
    pub richText: bool,
    pub scaleFactor: f32,
    pub fontStyle: i32,
    pub textAnchor: i32,
    pub alignByGeometry: bool,
    pub resizeTextForBestFit: bool,
    pub resizeTextMinSize: i32,
    pub resizeTextMaxSize: i32,
    pub updateBounds: bool,
    pub verticalOverflow: i32,
    pub horizontalOverflow: HorizontalWrapMode,
    pub generationExtents: Vector2_t,
    pub pivot: Vector2_t,
    pub generateOutOfBounds: bool,
}
pub const NotificationStyle_None: NotificationStyle = 0;
pub const NotificationStyle_BigTextStyle: NotificationStyle = 2;
pub type NotificationStyle = ::std::os::raw::c_int;
pub const GroupAlertBehaviours_GroupAlertAll: GroupAlertBehaviours = 0;
pub const GroupAlertBehaviours_GroupAlertSummary: GroupAlertBehaviours = 1;
pub const GroupAlertBehaviours_GroupAlertChildren: GroupAlertBehaviours = 2;
pub type GroupAlertBehaviours = ::std::os::raw::c_int;
#[repr(C)]
#[derive(Debug)]
pub struct AndroidNotification {
    pub Title: *mut Il2CppString,
    pub Text: *mut Il2CppString,
    pub SmallIcon: *mut Il2CppString,
    pub FireTime: u64,
    pub LargeIcon: *mut Il2CppString,
    pub Style: NotificationStyle,
    pub Number: i32,
    pub ShouldAutoCancel: bool,
    pub UsesStopwatch: bool,
    pub Group: *mut Il2CppString,
    pub GroupSummary: bool,
    pub GroupAlertBehaviour: GroupAlertBehaviours,
    pub SortKey: *mut Il2CppString,
    pub IntentData: *mut Il2CppString,
    pub ShowTimestamp: bool,
    pub ShowCustomTimestamp: bool,
    pub m_Color: Color_t,
    pub m_RepeatInterval: bool,
    pub m_CustomTimestamp: bool,
}
#[repr(C)]
#[derive(Debug, Deserialize, Serialize, Clone, Default)]
pub struct Resolution {
    pub width: i32,
    pub height: i32,
    pub refresh_rate: i32
}
#[repr(C)]
#[derive(Debug, Deserialize, Serialize, Clone, Default)]
pub struct RefreshRate {
    pub numerator: u32,
    pub denominator: u32
}

/************** ACTk (READ-ONLY) **************/
#[repr(C)]
#[derive(Default, Debug)]
pub struct ObscuredInt {
    current_crypto_key: i32,
    hidden_value: i32,
    inited: bool,
    fake_value: i32,
    fake_value_active: bool
}
impl ObscuredInt {
    pub fn value(&self) -> i32 {
        self.hidden_value ^ self.current_crypto_key
    }
}