{"configurations": [{"name": "Win32", "includePath": ["${workspaceFolder}/**", "${workspaceFolder}/GameSimulator", "${workspaceFolder}/GameSimulator/URA1.0,0", "${workspaceFolder}/GameSimulator/URA1.0.1", "${workspaceFolder}/GameSimulator/URA1.0.1/Game", "${workspaceFolder}/GameSimulator/URA1.0.1/GameDatabase", "${workspaceFolder}/Tools", "${workspaceFolder}/Config", "${workspaceFolder}/Database"], "defines": ["_DEBUG", "UNICODE", "_UNICODE", "_WIN32", "_CONSOLE"], "windowsSdkVersion": "10.0.22000.0", "compilerPath": "D:/Visual Studio/root_directory/VC/Tools/MSVC/14.44.35207/bin/HostX86/x86/cl.exe", "cStandard": "c17", "cppStandard": "c++17", "intelliSenseMode": "windows-msvc-x86", "configurationProvider": "ms-vscode.cpptools", "compilerArgs": ["/I${workspaceFolder}", "/I${workspaceFolder}/GameSimulator", "/I${workspaceFolder}/GameSimulator/URA1.0,0", "/I${workspaceFolder}/GameSimulator/URA1.0.1", "/I${workspaceFolder}/GameSimulator/URA1.0.1/Game", "/I${workspaceFolder}/GameSimulator/URA1.0.1/GameDatabase"], "forcedInclude": [], "browse": {"path": ["${workspaceFolder}/**"], "limitSymbolsToIncludedHeaders": true}}], "version": 4}