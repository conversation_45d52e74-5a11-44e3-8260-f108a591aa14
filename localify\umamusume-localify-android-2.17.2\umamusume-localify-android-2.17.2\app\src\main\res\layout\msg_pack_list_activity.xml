<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".activity.MsgPackListActivity">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:transitionGroup="true">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fitsSystemWindows="true"
            app:liftOnScroll="true">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/collapsing_layout"
                style="?collapsingToolbarLayoutLargeStyle"
                android:layout_width="match_parent"
                android:layout_height="?collapsingToolbarLayoutLargeSize"
                app:contentScrim="@android:color/transparent"
                app:layout_scrollFlags="scroll|snap|exitUntilCollapsed"
                app:statusBarScrim="@android:color/transparent">

                <com.google.android.material.appbar.MaterialToolbar
                    android:id="@+id/app_bar"
                    android:layout_width="match_parent"
                    android:layout_height="?actionBarSize"
                    android:elevation="0dp"
                    app:layout_collapseMode="pin">

                </com.google.android.material.appbar.MaterialToolbar>
            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <LinearLayout
            android:id="@+id/progress_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginHorizontal="56dp"
            android:layout_marginTop="-256dp"
            android:gravity="center"
            android:orientation="vertical"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <com.google.android.material.progressindicator.LinearProgressIndicator
                android:id="@+id/progress"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:indeterminate="true"
                app:hideAnimationBehavior="outward"
                app:showAnimationBehavior="inward"
                tools:indeterminate="false"
                tools:max="100"
                tools:progress="50" />

            <TextView
                android:id="@+id/progress_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                tools:text="100 / 200" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/empty_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:orientation="vertical"
            android:visibility="gone">

            <ImageView
                android:layout_width="128dp"
                android:layout_height="128dp"
                android:layout_gravity="center"
                android:contentDescription="@string/files_empty"
                android:src="@drawable/ic_file_empty" />

            <TextView
                style="@style/TextAppearance.Material3.BodyLarge"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@string/files_empty" />

        </LinearLayout>

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/refresh_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                android:fitsSystemWindows="false"
                android:orientation="vertical"
                android:scrollbars="vertical"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:listitem="@layout/msg_pack_item" />

        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/top_fab"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|end"
            android:layout_marginStart="0dp"
            android:layout_marginTop="0dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="16dp"
            android:src="@drawable/ic_upward" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>
