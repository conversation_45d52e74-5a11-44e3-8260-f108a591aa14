## 1.5.0

- Add japanese translation
- Add Dump MsgPack settings

## 1.4.2

- Add character dialogue caption option

## 1.4.1

- Fixed error that occur under Android 12

## 1.4.0

- Add various preferences
- Fixed an error where the app could not run after removing some packages
- Enable enableOnBackInvokedCallback
- Update themed icon
- Changed the way the settings app looks up module versions

## 1.3.0

- Add some preferences
- Update library, proguard rules
- Add TextMeshPro font asset name option

## 1.2.0

- Add 'staticEntriesUseHash' option
- Hide Restore notification option

## 1.1.1

- Fix cannot select json files on Android Pie and earlier
- Fix crash when file does not exist in dict list
- Fix wrong background color when blur disabled on Android S and later
- 
## 1.1.0

- Add Traditional chinese package (MyCard version)
- Show installed module version
- Add restore notification option (KOR only)

## 1.0.2

- Add Traditional chinese package
- Enable minify

## 1.0.1

- Fix infinity loading if document tree access permission revoked

## 1.0.0

- Initial release.