<?xml version="1.0" encoding="utf-8"?><!--
  Copyright (C) 2021 The Android Open Source Project

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="?android:attr/listPreferredItemHeight"
    android:orientation="vertical"
    android:paddingStart="?android:attr/listPreferredItemPaddingStart"
    android:paddingTop="@dimen/settingslib_switchbar_margin"
    android:paddingEnd="?android:attr/listPreferredItemPaddingEnd"
    android:paddingBottom="@dimen/settingslib_switchbar_margin">

    <LinearLayout
        android:id="@+id/frame"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="@dimen/settingslib_min_switch_bar_height"
        android:paddingStart="@dimen/settingslib_switchbar_padding_left"
        android:paddingEnd="@dimen/settingslib_switchbar_padding_right">

        <TextView
            android:id="@+id/switch_text"
            style="@style/MainSwitchText.Settingslib"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginVertical="@dimen/settingslib_switch_title_margin"
            android:layout_marginEnd="@dimen/settingslib_switch_title_margin"
            android:layout_weight="1"
            android:ellipsize="end"
            android:textAppearance="?android:attr/textAppearanceListItem"
            tools:text="Main preference" />

        <com.google.android.material.materialswitch.MaterialSwitch
            android:id="@android:id/switch_widget"
            android:layout_width="wrap_content"
            android:layout_height="48dp"
            android:layout_gravity="center_vertical"
            android:clickable="false"
            android:focusable="false" />
    </LinearLayout>

</LinearLayout>
