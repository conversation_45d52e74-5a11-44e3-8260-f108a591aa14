## 2.17.0

- Enhanced to allow for more Texture2D replacements
- Add use third party news option (KOR only)

## 2.16.2

- x86: Fix error where libs are only partially copied

## 2.16.1

- Immediately hide captions when voice stops

## 2.16.0

- Dump/notify messagepack
- Show Gallop WebView instead of <PERSON><PERSON><PERSON> WebView on Korean version
- Show DialogAnnounceEvent on click of story event announcement voice
- Setting the maximum number of items to donate when donating circle items
- Fixed an issue where reinstalling an app on an x86 environment might not launch the app

## 2.15.2

- Enhanced NowLoading fallback logic

## 2.15.1

- Fixed an issue where the resolution was incorrect when the screen was rotated from horizontal to vertical

## 2.15.0

- Lookup NativeBridgeItf symbol from ro.dalvik.vm.native.bridge (Improved to work on Waydroid)
- Fixed an issue where nothing could be done when the loading screen failed to display, additionally added an option to hide the loading screen
- Improved display of gacha video & story summary video in force landscape mode
- Change the maximum number of characters to wrap with newlines
- Add cyspring update mode option
- Add character system text caption
- Use another DB when querying text from DB
- Changed the way the settings app looks up module versions

## 2.7.3

- BlueStacks Android 11 support

## 2.7.2

- Fixed a crash that occurred when closing the download dialog in the Korean version

## 2.7.1

- Enhanced force landscape on Unity 2019 build

## 2.7.0

- Applying many Windows version feature updates
- Enhanced Notification.build hook
- Load replacement asset on criware init
- Hooking CriMana.Player.SetFile to change movie file
- Custom TextMeshPro font support

## 2.5.0

- Enhanced notification java logic
- Add 'staticEntriesUseHash' option
- Cyan.Loader.AssetLoader.LoadOne -> UnityEngine.AssetBundle.LoadFromFile
- Improved movie size in force landscape mode

## 2.4.0

- Bypass root check
- Add Traditional chinese package (MyCard version)
- Restore notification on korean version

## 2.3.3

- Fix module not working where using NativeBridge (x86)

## 2.3.2

- Add Traditional chinese package

## 2.3.1

- Improved force landscape mode behavior on Unity 2019

## 2.3.0

- Improved dict management
- Added uiLoadingShowOrientationGuide option
- Improved force landscape mode behavior
- Disable NativeBridge hooking in Riru module
- New: Add Settings App

## 2.2.0

- Add Force landscape mode

## 2.1.6

- BlueStacks Support

## 2.1.5

- Fixed an issue that could cause NullReferenceException in Unity when loading some replacement assets

## 2.1.4

- Fix wrong landscape resolution on Korean client (Unity 2019)

## 2.1.3

- Fix detecting package on NativeBridge

## 2.1.2

- Fix crash on Korean client

## 2.1.1

- Fix update url

## 2.1.0

- Riru Support

## 2.0.0

- x86 NativeBridge Hooking support
- Support replace to custom font
- Add ui animation scale option
- Add graphics settings
- Support replace downloaded assets in the game
- Code stabilization

## 1.0.0

- Initial release.