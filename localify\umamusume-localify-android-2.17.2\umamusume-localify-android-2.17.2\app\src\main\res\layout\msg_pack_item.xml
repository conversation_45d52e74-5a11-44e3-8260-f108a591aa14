<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?android:selectableItemBackground"
        android:baselineAligned="false"
        android:gravity="center_vertical"
        android:minHeight="?android:listPreferredItemHeight"
        android:paddingStart="16dip"
        android:paddingEnd="0dip">

        <FrameLayout
            android:id="@+id/icon_frame"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@android:id/icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:maxHeight="48dp"
                app:maxWidth="48dp"
                tools:src="@drawable/ic_response" />
        </FrameLayout>

        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dip"
            android:layout_marginTop="6dip"
            android:layout_marginEnd="6dip"
            android:layout_marginBottom="6dip"
            android:layout_weight="1">

            <TextView
                android:id="@+id/file_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="marquee"
                android:fadingEdge="horizontal"
                android:singleLine="true"
                android:textAppearance="?android:textAppearanceMedium"
                android:textColor="?android:textColorPrimary"
                tools:text="@tools:sample/date/mmddyy" />

            <TextView
                android:id="@+id/type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/file_name"
                android:layout_alignStart="@+id/file_name"
                android:maxLines="4"
                android:textAppearance="?android:textAppearanceSmall"
                android:textColor="?android:textColorSecondary"
                tools:text="Response" />

        </RelativeLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/file_length"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="0dp"
                android:layout_marginEnd="16dp"
                android:textAppearance="?android:textAppearanceListItem"
                tools:text="1.3 MB" />

            <ImageView
                android:id="@+id/expand"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="0dp"
                android:layout_marginEnd="16dp"
                android:src="@drawable/ic_expand_more"
                android:visibility="gone"
                tools:visibility="visible" />

            <com.google.android.material.checkbox.MaterialCheckBox
                android:id="@+id/select"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="-8dip"
                android:background="@null"
                android:clickable="false"
                android:focusable="false"
                android:visibility="gone"
                tools:visibility="visible" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/remove_button"
                style="@style/Widget.Material3.Button.IconButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:icon="@drawable/ic_close"
                tools:visibility="visible" />

        </LinearLayout>
    </LinearLayout>
</layout>
