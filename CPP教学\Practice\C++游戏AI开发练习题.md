# C++游戏AI开发练习题

## 🎯 练习说明

本练习题专门针对UmaAI项目开发，通过实际编程练习帮助您掌握项目中用到的C++语法。

**练习方式**：
1. 在 `umaAi_ForURA/Practice/` 目录下创建练习文件
2. 每个练习创建一个 `.cpp` 文件
3. 编译运行验证结果
4. 对照答案检查实现

---

## 📚 练习1：基础语法和变量

### 题目1.1：游戏状态初始化
创建一个程序，初始化赛马娘的基本属性：

**要求**：
- 声明5个整数变量表示五维属性（速度、耐力、力量、根性、智慧）
- 初始值都设为100
- 声明体力变量，初始值为100
- 声明回合数变量，初始值为0
- 输出所有变量的值

**文件名**：`practice1_1.cpp`

**期望输出**：
```
速度: 100
耐力: 100
力量: 100
根性: 100
智慧: 100
体力: 100
回合: 0
```

### 题目1.2：条件判断
编写一个函数，根据体力值决定推荐动作：

**要求**：
- 体力 < 30：推荐休息
- 体力 30-70：推荐外出
- 体力 > 70：推荐训练
- 使用if-else语句实现

**文件名**：`practice1_2.cpp`

---

## 📚 练习2：结构体和类

### 题目2.1：创建马娘结构体
定义一个表示马娘的结构体：

**要求**：
```cpp
struct Uma {
    std::string name;        // 名字
    int speed, stamina, power, guts, wisdom;  // 五维属性
    int vital;               // 体力
    int turn;                // 当前回合
    
    // 添加构造函数，设置默认值
    // 添加方法：getTotalStatus() 返回五维属性总和
    // 添加方法：isLowVital() 判断体力是否低于30
};
```

**文件名**：`practice2_1.cpp`

### 题目2.2：简单的游戏模拟器类
创建一个简单的游戏模拟器：

**要求**：
```cpp
class SimpleSimulator {
private:
    Uma uma;
    int max_turns;
    
public:
    // 构造函数：初始化马娘和最大回合数
    // startGame()：开始游戏
    // nextTurn()：进入下一回合
    // isGameEnd()：判断游戏是否结束
    // getUma()：获取马娘状态
};
```

**文件名**：`practice2_2.cpp`

---

## 📚 练习3：容器和数组

### 题目3.1：技能管理
使用vector管理马娘的技能：

**要求**：
- 创建一个vector存储技能ID（整数）
- 实现添加技能功能
- 实现查找技能功能
- 实现显示所有技能功能
- 测试添加技能：101, 102, 103

**文件名**：`practice3_1.cpp`

### 题目3.2：支援卡分配
模拟支援卡随机分配到训练位置：

**要求**：
- 创建一个包含6个支援卡ID的数组
- 创建一个包含5个训练位置的数组
- 随机将支援卡分配到训练位置（每个位置最多1张卡）
- 输出分配结果

**文件名**：`practice3_2.cpp`

---

## 📚 练习4：枚举和函数

### 题目4.1：动作枚举
定义训练动作的枚举：

**要求**：
```cpp
enum TrainingType {
    SPEED = 0,
    STAMINA = 1,
    POWER = 2,
    GUTS = 3,
    WISDOM = 4,
    REST = 5,
    OUTGOING = 6
};

// 实现函数：
// std::string getActionName(TrainingType type)  // 返回动作名称
// bool isTraining(TrainingType type)            // 判断是否为训练动作
```

**文件名**：`practice4_1.cpp`

### 题目4.2：训练收益计算
实现训练收益计算函数：

**要求**：
```cpp
// 计算训练收益
int calculateGain(int base_value, int support_count, bool is_shining, int motivation) {
    // base_value: 基础收益
    // support_count: 支援卡数量 (0-5)
    // is_shining: 是否有闪光卡
    // motivation: 干劲等级 (1-5)
    
    // 计算公式：
    // 收益 = base_value + support_count * 5
    // 如果有闪光卡：收益 *= 1.5
    // 干劲加成：收益 *= (0.8 + motivation * 0.1)
    
    return 计算结果;
}
```

**文件名**：`practice4_2.cpp`

---

## 📚 练习5：随机数和游戏逻辑

### 题目5.1：训练成功判定
实现训练成功率判定：

**要求**：
- 使用std::mt19937生成随机数
- 实现函数判断训练是否成功
- 测试不同成功率：20%, 50%, 80%
- 运行1000次统计实际成功率

**文件名**：`practice5_1.cpp`

### 题目5.2：随机事件系统
实现简单的随机事件：

**要求**：
```cpp
struct Event {
    int id;
    std::string name;
    int status_gain[5];  // 五维属性获得
    int vital_change;    // 体力变化
};

// 创建3个不同的事件
// 实现随机选择事件的函数
// 实现应用事件效果的函数
```

**文件名**：`practice5_2.cpp`

---

## 📚 练习6：文件操作

### 题目6.1：保存游戏结果
将游戏结果保存到文件：

**要求**：
- 创建一个游戏结果结构体
- 实现保存结果到文本文件的函数
- 实现从文件读取结果的函数
- 测试保存和读取功能

**文件名**：`practice6_1.cpp`

### 题目6.2：配置文件读取
从配置文件读取游戏参数：

**要求**：
- 创建一个简单的配置文件 `config.txt`
- 格式：`参数名=值`
- 实现读取配置的函数
- 支持读取：max_turns, base_vital, training_cost

**文件名**：`practice6_2.cpp`

---

## 📚 练习7：综合应用

### 题目7.1：简化版AI
实现一个简单的训练AI：

**要求**：
```cpp
class SimpleAI {
public:
    TrainingType selectAction(const Uma& uma) {
        // 实现简单的决策逻辑：
        // 1. 体力 < 30 时休息
        // 2. 否则训练最低的属性
        // 3. 如果所有属性相等，训练速度
    }
};
```

**文件名**：`practice7_1.cpp`

### 题目7.2：完整游戏循环
实现一个完整的简化游戏：

**要求**：
- 整合前面的所有组件
- 实现78回合的游戏循环
- 每回合：选择动作 → 执行动作 → 检查事件 → 下一回合
- 输出最终结果

**文件名**：`practice7_2.cpp`

---

## 🔧 编译和运行指南

### 编译单个练习
```cmd
# 在项目根目录下
cl /EHsc /std:c++17 Practice\practice1_1.cpp /Fe:Practice\practice1_1.exe
```

### 运行练习
```cmd
cd Practice
practice1_1.exe
```

### 批量编译脚本
创建 `compile_practice.bat`：
```batch
@echo off
echo Compiling all practice files...

for %%f in (Practice\*.cpp) do (
    echo Compiling %%f...
    cl /EHsc /std:c++17 "%%f" /Fe:"%%~dpnf.exe" /Fo:Practice\
)

echo Done!
pause
```

---

## 📝 练习提示

### 练习1-2：基础语法
- 重点掌握变量声明、条件语句
- 注意C++的输入输出语法
- 练习使用std::cout和std::endl

### 练习3-4：结构体和容器
- 理解结构体和类的区别
- 掌握vector的基本操作
- 学会使用枚举提高代码可读性

### 练习5-6：高级功能
- 掌握现代C++的随机数生成
- 学会基本的文件操作
- 理解错误处理的重要性

### 练习7：综合应用
- 整合所有学过的知识
- 体验完整的程序设计过程
- 为实际项目开发做准备

---

## 🎯 学习建议

### 循序渐进
1. **先做简单练习** - 从练习1开始，逐步进阶
2. **理解再编码** - 先理解题目要求，再开始编程
3. **测试验证** - 每个练习都要编译运行，确保正确

### 遇到问题时
1. **查看教学文档** - 对应的语法说明
2. **参考项目代码** - 看看实际项目中如何使用
3. **使用编译器提示** - Visual Studio会给出有用的错误信息
4. **逐步调试** - 使用cout输出中间结果

### 扩展练习
完成基础练习后，可以尝试：
1. **优化算法** - 让AI决策更智能
2. **添加功能** - 实现更多游戏机制
3. **改进界面** - 让输出更美观
4. **性能测试** - 测试程序运行效率

---

## 🏆 完成标准

每个练习完成后，应该能够：
- ✅ 成功编译，无错误和警告
- ✅ 运行结果符合预期
- ✅ 代码结构清晰，有适当注释
- ✅ 理解所使用的C++概念

**准备好开始练习了吗？从练习1开始，一步步掌握C++游戏AI开发！** 🚀
