# C++ 环境配置测试项目

这个项目用于测试您的Visual Studio C++环境配置是否正确。

## 项目结构

```
CppTest/
├── main.cpp                    # 主测试程序
├── .vscode/                    # VS Code配置文件夹
│   ├── c_cpp_properties.json   # C++智能感知配置
│   ├── tasks.json              # 编译任务配置
│   ├── launch.json             # 调试配置
│   └── settings.json           # 项目设置
└── README.md                   # 说明文档
```

## 测试内容

这个程序会测试以下C++功能：

1. **编译器信息检测**
   - 编译器版本
   - 平台信息
   - C++标准版本
   - 构建模式

2. **基本功能测试**
   - 输入输出
   - STL容器（vector）
   - 类和对象

3. **高级功能测试**
   - Lambda表达式
   - 智能指针
   - 范围for循环
   - 多线程

## 使用方法

### 方法1：使用Visual Studio（推荐）

1. 打开Visual Studio
2. 选择"打开文件夹"
3. 选择CppTest文件夹
4. 右键点击main.cpp，选择"设为启动项"
5. 按Ctrl+F5运行

### 方法2：使用VS Code

1. 用VS Code打开CppTest文件夹
2. 确保已安装C/C++扩展
3. 打开main.cpp文件
4. 使用以下任一方式运行：

#### 编译和调试：
- 按F5开始调试
- 或按Ctrl+Shift+P，输入"Tasks: Run Task"，选择编译任务

#### 快速运行：
- 按Ctrl+Shift+P，输入"Tasks: Run Task"
- 选择"编译并运行"

#### 使用Code Runner插件：
- 安装Code Runner扩展
- 按Ctrl+Alt+N快速运行

### 方法3：命令行编译

打开Developer Command Prompt for VS，在项目目录下执行：

```cmd
cl /EHsc /nologo /std:c++17 main.cpp /Femain.exe
main.exe
```

## 预期输出

如果配置正确，您应该看到类似以下的输出：

```
=== C++ 环境配置测试程序 ===
测试Visual Studio C++编译器配置

=== 编译器信息 ===
编译器: Microsoft Visual C++ 1930
平台: Windows
C++标准: 201703
构建模式: Debug

=== 基本功能测试 ===
请输入您的名字: [您的输入]
你好, [您的名字]!
测试vector: 1 2 3 4 5 
Name: 测试对象, Value: 42

=== 高级功能测试 ===
Lambda测试: 3 + 4 = 7
Name: 智能指针测试, Value: 100
范围for循环测试: Hello World C++ Test 
多线程测试: 线程2 线程1 完成

=== 测试完成 ===
所有测试通过！C++环境配置正确。

按任意键退出...
```

## 故障排除

### 1. 编译器找不到
如果出现"cl.exe不是内部或外部命令"错误：
- 确保已安装Visual Studio并包含C++工具
- 使用"Developer Command Prompt for VS"
- 或在VS Code中打开"Developer PowerShell"

### 2. 路径问题
如果编译器路径不正确，请修改`.vscode/c_cpp_properties.json`中的`compilerPath`：
```json
"compilerPath": "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.XX.XXXXX/bin/Hostx64/x64/cl.exe"
```

### 3. 智能感知问题
如果代码高亮或智能感知不工作：
- 按Ctrl+Shift+P，输入"C/C++: Reset IntelliSense Database"
- 检查`.vscode/c_cpp_properties.json`配置

### 4. 中文编码问题
如果中文显示乱码：
- 在VS Code设置中添加：`"files.encoding": "utf8"`
- 或使用`chcp 65001`命令设置控制台编码

## 下一步

如果测试通过，您可以：
1. 开始开发自己的C++项目
2. 尝试编译UmaAi项目
3. 学习更多C++高级特性

## 需要帮助？

如果遇到问题，请检查：
1. Visual Studio是否正确安装
2. C++工具是否已安装
3. 环境变量是否正确设置
4. VS Code C++扩展是否已安装
