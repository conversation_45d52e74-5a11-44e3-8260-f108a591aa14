plugins {
    id 'com.android.application'
}

android {
    namespace 'com.kimjio.umamusumelocalify.settings'
    compileSdk rootProject.ext.targetSdkVersion
    ndkVersion '25.0.8775105'

    defaultConfig {
        applicationId "com.kimjio.umamusumelocalify.settings"
        minSdk rootProject.ext.minSdkVersion
        targetSdk rootProject.ext.targetSdkVersion
        versionCode 14
        versionName "1.6.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        debug {
            pseudoLocalesEnabled true
            jniDebuggable true
        }
        release {
            pseudoLocalesEnabled true
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    buildFeatures {
        buildConfig true
        dataBinding true
    }
    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    externalNativeBuild {
        cmake {
            path "src/main/cpp/CMakeLists.txt"
            version "3.22.1"
        }
    }
}

dependencies {
    constraints {
        implementation("org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0") {
            because("kotlin-stdlib-jdk7 is now a part of kotlin-stdlib")
        }
        implementation("org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.0") {
            because("kotlin-stdlib-jdk8 is now a part of kotlin-stdlib")
        }
    }
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.3'
    implementation 'androidx.appcompat:appcompat:1.7.0-alpha02'
    implementation 'com.google.android.material:material:1.9.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.preference:preference:1.2.0'
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'
    implementation 'androidx.window:window:1.0.0'
    implementation 'it.xabaras.android:recyclerview-swipedecorator:1.4'
    implementation 'org.msgpack:msgpack-core:0.9.3'
    implementation 'org.msgpack:jackson-dataformat-msgpack:0.9.3'
    implementation 'com.blacksquircle.ui:editorkit:2.7.0'
    implementation 'com.blacksquircle.ui:language-json:2.7.0'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}