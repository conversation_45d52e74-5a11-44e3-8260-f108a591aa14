<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="MainSwitchBar">
        <!-- The summary for the Preference in a PreferenceActivity screen when the
             SwitchPreference is checked. If separate on/off summaries are not
             needed, the summary attribute can be used instead. -->
        <attr name="summaryOn"/>
        <attr name="android:summaryOn"/>
        <!-- The summary for the Preference in a PreferenceActivity screen when the
             SwitchPreference is unchecked. If separate on/off summaries are not
             needed, the summary attribute can be used instead. -->
        <attr name="summaryOff"/>
        <attr name="android:summaryOff"/>
        <!-- The text used on the switch itself when in the "on" state.
             This should be a very SHORT string, as it appears in a small space. -->
        <attr format="string" name="switchTextOn"/>
        <attr name="android:switchTextOn"/>
        <!-- The text used on the switch itself when in the "off" state.
             This should be a very SHORT string, as it appears in a small space. -->
        <attr format="string" name="switchTextOff"/>
        <attr name="android:switchTextOff"/>
        <!-- The state (true for on, or false for off) that causes dependents to be disabled. By default,
             dependents will be disabled when this is unchecked, so the value of this preference is false. -->
        <attr name="disableDependentsState"/>
        <attr name="android:disableDependentsState"/>
        <attr name="android:title" />
    </declare-styleable>
</resources>
