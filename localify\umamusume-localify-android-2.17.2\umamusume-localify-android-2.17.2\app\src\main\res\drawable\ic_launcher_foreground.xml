<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="108dp"
    android:height="108dp"
    android:tint="#B34E8E"
    android:viewportWidth="24"
    android:viewportHeight="24">
    <group
        android:scaleX="0.58"
        android:scaleY="0.58"
        android:translateX="5.04"
        android:translateY="5.04">
        <path
            android:fillAlpha="0.3"
            android:fillColor="@android:color/white"
            android:pathData="M19.28,8.6l-0.7,-1.21 -1.27,0.51 -1.06,0.43 -0.91,-0.7c-0.39,-0.3 -0.8,-0.54 -1.23,-0.71l-1.06,-0.43 -0.16,-1.13L12.7,4h-1.4l-0.19,1.35 -0.16,1.13 -1.06,0.44c-0.41,0.17 -0.82,0.41 -1.25,0.73l-0.9,0.68 -1.05,-0.42 -1.27,-0.52 -0.7,1.21 1.08,0.84 0.89,0.7 -0.14,1.13c-0.03,0.3 -0.05,0.53 -0.05,0.73s0.02,0.43 0.05,0.73l0.14,1.13 -0.89,0.7 -1.08,0.84 0.7,1.21 1.27,-0.51 1.06,-0.43 0.91,0.7c0.39,0.3 0.8,0.54 1.23,0.71l1.06,0.43 0.16,1.13 0.19,1.36h1.39l0.19,-1.35 0.16,-1.13 1.06,-0.43c0.41,-0.17 0.82,-0.41 1.25,-0.73l0.9,-0.68 1.04,0.42 1.27,0.51 0.7,-1.21 -1.08,-0.84 -0.89,-0.7 0.14,-1.13c0.04,-0.31 0.05,-0.52 0.05,-0.73 0,-0.21 -0.02,-0.43 -0.05,-0.73l-0.14,-1.13 0.89,-0.7 1.1,-0.84zM12,16c-2.21,0 -4,-1.79 -4,-4s1.79,-4 4,-4 4,1.79 4,4 -1.79,4 -4,4z"
            android:strokeAlpha="0.3" />
        <path
            android:fillColor="@android:color/white"
            android:pathData="M19.43,12.98c0.04,-0.32 0.07,-0.64 0.07,-0.98 0,-0.34 -0.03,-0.66 -0.07,-0.98l2.11,-1.65c0.19,-0.15 0.24,-0.42 0.12,-0.64l-2,-3.46c-0.09,-0.16 -0.26,-0.25 -0.44,-0.25 -0.06,0 -0.12,0.01 -0.17,0.03l-2.49,1c-0.52,-0.4 -1.08,-0.73 -1.69,-0.98l-0.38,-2.65C14.46,2.18 14.25,2 14,2h-4c-0.25,0 -0.46,0.18 -0.49,0.42l-0.38,2.65c-0.61,0.25 -1.17,0.59 -1.69,0.98l-2.49,-1c-0.06,-0.02 -0.12,-0.03 -0.18,-0.03 -0.17,0 -0.34,0.09 -0.43,0.25l-2,3.46c-0.13,0.22 -0.07,0.49 0.12,0.64l2.11,1.65c-0.04,0.32 -0.07,0.65 -0.07,0.98s0.03,0.66 0.07,0.98l-2.11,1.65c-0.19,0.15 -0.24,0.42 -0.12,0.64l2,3.46c0.09,0.16 0.26,0.25 0.44,0.25 0.06,0 0.12,-0.01 0.17,-0.03l2.49,-1c0.52,0.4 1.08,0.73 1.69,0.98l0.38,2.65c0.03,0.24 0.24,0.42 0.49,0.42h4c0.25,0 0.46,-0.18 0.49,-0.42l0.38,-2.65c0.61,-0.25 1.17,-0.59 1.69,-0.98l2.49,1c0.06,0.02 0.12,0.03 0.18,0.03 0.17,0 0.34,-0.09 0.43,-0.25l2,-3.46c0.12,-0.22 0.07,-0.49 -0.12,-0.64l-2.11,-1.65zM17.45,11.27c0.04,0.31 0.05,0.52 0.05,0.73 0,0.21 -0.02,0.43 -0.05,0.73l-0.14,1.13 0.89,0.7 1.08,0.84 -0.7,1.21 -1.27,-0.51 -1.04,-0.42 -0.9,0.68c-0.43,0.32 -0.84,0.56 -1.25,0.73l-1.06,0.43 -0.16,1.13 -0.2,1.35h-1.4l-0.19,-1.35 -0.16,-1.13 -1.06,-0.43c-0.43,-0.18 -0.83,-0.41 -1.23,-0.71l-0.91,-0.7 -1.06,0.43 -1.27,0.51 -0.7,-1.21 1.08,-0.84 0.89,-0.7 -0.14,-1.13c-0.03,-0.31 -0.05,-0.54 -0.05,-0.74s0.02,-0.43 0.05,-0.73l0.14,-1.13 -0.89,-0.7 -1.08,-0.84 0.7,-1.21 1.27,0.51 1.04,0.42 0.9,-0.68c0.43,-0.32 0.84,-0.56 1.25,-0.73l1.06,-0.43 0.16,-1.13 0.2,-1.35h1.39l0.19,1.35 0.16,1.13 1.06,0.43c0.43,0.18 0.83,0.41 1.23,0.71l0.91,0.7 1.06,-0.43 1.27,-0.51 0.7,1.21 -1.07,0.85 -0.89,0.7 0.14,1.13zM12,8c-2.21,0 -4,1.79 -4,4s1.79,4 4,4 4,-1.79 4,-4 -1.79,-4 -4,-4zM12,14c-1.1,0 -2,-0.9 -2,-2s0.9,-2 2,-2 2,0.9 2,2 -0.9,2 -2,2z" />
    </group>
</vector>
