<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/left_prefix"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            tools:text="'"/>

        <TextView
            android:id="@+id/left"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            tools:text="key"/>

        <TextView
            android:id="@+id/left_suffix"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            tools:text="'"/>

        <ImageView
            android:id="@+id/expand"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/ic_expand_more" />

        <TextView
            android:id="@+id/right_prefix"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/right"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:singleLine="true"
            android:textIsSelectable="true"
            android:textSize="14sp"
            tools:text=""/>

        <TextView
            android:id="@+id/right_suffix"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="14sp" />

    </LinearLayout>
</layout>