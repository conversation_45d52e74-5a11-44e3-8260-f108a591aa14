<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?android:selectableItemBackground"
        android:baselineAligned="false"
        android:gravity="center_vertical"
        android:minHeight="?android:listPreferredItemHeight"
        android:paddingStart="16dip"
        android:paddingEnd="0dip">

        <FrameLayout
            android:id="@+id/icon_frame"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@android:id/icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_json"
                app:maxHeight="48dp"
                app:maxWidth="48dp" />
        </FrameLayout>

        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dip"
            android:layout_marginTop="6dip"
            android:layout_marginEnd="6dip"
            android:layout_marginBottom="6dip"
            android:layout_weight="1">

            <TextView
                android:id="@+id/file_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="marquee"
                android:fadingEdge="horizontal"
                android:singleLine="true"
                android:textAppearance="?android:textAppearanceMedium"
                android:textColor="?android:textColorPrimary"
                tools:text="static.json" />

            <TextView
                android:id="@+id/support_version"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/file_name"
                android:layout_alignStart="@+id/file_name"
                android:maxLines="4"
                android:textAppearance="?android:textAppearanceSmall"
                android:textColor="?android:textColorSecondary"
                tools:text="지원 버전: 1.0.*" />

        </RelativeLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/error_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:contentDescription="@string/error_occurred"
                android:paddingHorizontal="4dp"
                android:src="@drawable/ic_error"
                android:visibility="gone"
                tools:visibility="visible" />

            <com.google.android.material.checkbox.MaterialCheckBox
                android:id="@+id/use_translate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="-8dip"
                android:background="@null"
                android:clickable="false"
                android:focusable="false" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/remove_button"
                style="@style/Widget.Material3.Button.IconButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:icon="@drawable/ic_close" />

        </LinearLayout>
    </LinearLayout>
</layout>
