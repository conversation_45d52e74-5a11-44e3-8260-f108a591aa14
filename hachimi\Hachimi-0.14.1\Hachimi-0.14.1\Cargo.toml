[package]
name = "hachimi"
version = "0.14.1"
rust-version = "1.77"
edition = "2021"

[lib]
name = "hachimi"
crate-type = ["cdylib"]

[profile.release]
strip = true

[profile.dev]
debug = "limited"

[dependencies]
log = "0.4"
cstr = "0.2"
once_cell = "1.19"
arc-swap = "1.7"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
fnv = "1.0"
widestring = "1.1"
egui = { version = "0.27", default-features = false }
egui_extras = { version = "0.27", default-features = false, features = ["image"] }
image = { version = "0.24", default-features = false, features = ["png"] }
png = "0.17"
sqlparser = "0.43"
textwrap = "0.16"
atomic_float = "0.1"
blake3 = "1.5"
ureq = { version = "2.10", features = ["json"] }
size = "0.4"
threadpool = "1.8"
zip = { version = "0.6", default-features = false, features = ["deflate"] }
unicode-width = "0.1"
tiny_http = "0.12"
rust-i18n = "3.1"

[target.'cfg(target_os = "android")'.dependencies]
libc = "0.2"
android_logger = "0.13"
procfs = "0.16"
jni = "0.21"
egui_glow = "0.27"
glow = "0.13"
dobby-rs = "0.1"

[target.'cfg(target_os = "windows")'.dependencies]
windebug_logger = "0.1"
minhook = "0.5"
egui-directx11 = "0.4"
pelite = "0.10"

[target.'cfg(target_os = "windows")'.dependencies.windows]
version = "0.56"
features = [
    "Win32_System_LibraryLoader",
    "Win32_System_Memory",
    "Win32_System_SystemInformation",
    "Win32_System_DataExchange",
    "Win32_System_SystemServices",
    "Win32_System_Ole",
    "Win32_System_Diagnostics",
    "Win32_System_Diagnostics_ToolHelp",
    "Win32_System_Threading",
    "Win32_UI_WindowsAndMessaging",
    "Win32_UI_Input_KeyboardAndMouse",
    "Win32_UI_Shell",
    "Win32_Graphics_Dxgi",
    "Win32_Storage_FileSystem",
    "Win32_Graphics_Gdi"
]

[build-dependencies]
tauri-winres = "0.1"
cc = "1.1"

[package.metadata.tauri-winres]
ProductName = "Hachimi"
FileDescription = "Game enhancement and translation mod"

[patch.crates-io]
dobby-sys = { git = "https://github.com/LeadRDRK/dobby-sys.git", rev = "e005f6abb0db1353b92ff58d50f59112f04dcad4" }
embed-resource = { git = "https://github.com/nabijaczleweli/rust-embed-resource.git", rev = "e98e9aa28d66b0c2ff43516b41847cac372310dd" }