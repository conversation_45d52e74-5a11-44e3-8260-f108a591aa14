{"version": "2.0.0", "tasks": [{"type": "cppbuild", "label": "C/C++: cl.exe 生成活动文件", "command": "cl.exe", "args": ["/Zi", "/EHsc", "/nologo", "/std:c++17", "/Fe${fileDirname}\\${fileBasenameNoExtension}.exe", "${file}"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$msCompile"], "group": {"kind": "build", "isDefault": true}, "detail": "编译器: cl.exe"}, {"type": "shell", "label": "编译并运行", "command": "cl.exe", "args": ["/EHsc", "/nologo", "/std:c++17", "/Fe${fileDirname}\\${fileBasenameNoExtension}.exe", "${file}", "&&", "${fileDirname}\\${fileBasenameNoExtension}.exe"], "options": {"cwd": "${fileDirname}"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$msCompile"]}]}