{"files.associations": {"*.cpp": "cpp", "*.h": "c", "*.hpp": "cpp", "*.c": "c"}, "files.encoding": "utf8", "files.autoGuessEncoding": true, "files.eol": "\r\n", "C_Cpp.default.cppStandard": "c++17", "C_Cpp.default.cStandard": "c17", "C_Cpp.default.intelliSenseMode": "windows-msvc-x64", "C_Cpp.default.compilerPath": "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.40.33807/bin/Hostx64/x64/cl.exe", "C_Cpp.errorSquiggles": "enabled", "C_Cpp.intelliSenseEngine": "default", "C_Cpp.autocomplete": "default", "code-runner.executorMap": {"cpp": "cd $dir && cl /EHsc /std:c++17 /I\"${workspaceFolder}\" /I\"${workspaceFolder}\\GameSimulator\" $fileName /Fe$fileNameWithoutExt.exe && $fileNameWithoutExt.exe"}, "code-runner.runInTerminal": true, "code-runner.saveFileBeforeRun": true, "code-runner.clearPreviousOutput": true, "code-runner.preserveFocus": false, "terminal.integrated.defaultProfile.windows": "Developer PowerShell for VS 2022", "terminal.integrated.profiles.windows": {"Developer PowerShell for VS 2022": {"path": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\Tools\\VsDevCmd.bat", "args": ["&", "powershell"], "icon": "terminal-powershell"}}, "cmake.configureOnOpen": false, "editor.formatOnSave": true, "editor.tabSize": 4, "editor.insertSpaces": true, "editor.detectIndentation": true}