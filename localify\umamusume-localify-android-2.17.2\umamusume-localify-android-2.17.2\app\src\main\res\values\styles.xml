<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Preference.SwitchPreference.Material3" parent="Preference.SwitchPreference.Material">
        <item name="android:widgetLayout">@layout/preference_widget_material_switch</item>
    </style>

    <style name="Widget.Material3.Button.TextButton.Dialog.FullWidth" parent="Widget.Material3.Button.TextButton.Dialog">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:maxWidth">@null</item>
    </style>

    <style name="ThemeOverlay.Material3.Dialog.Centered" parent="ThemeOverlay.Material3.MaterialAlertDialog.Centered">
        <!-- Mark spacer as gone when showing full width buttons -->
        <item name="materialAlertDialogButtonSpacerVisibility">@integer/mtrl_view_gone</item>
        <item name="buttonBarPositiveButtonStyle">@style/Widget.Material3.Button.TextButton.Dialog.FullWidth</item>
        <item name="buttonBarNegativeButtonStyle">@style/Widget.Material3.Button.TextButton.Dialog.FullWidth</item>
        <item name="buttonBarNeutralButtonStyle">@style/Widget.Material3.Button.TextButton.Dialog.FullWidth</item>
    </style>
</resources>
