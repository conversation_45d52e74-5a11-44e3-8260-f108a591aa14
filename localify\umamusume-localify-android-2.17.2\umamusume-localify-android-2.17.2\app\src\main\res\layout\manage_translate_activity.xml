<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".activity.ManageTranslateActivity">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fitsSystemWindows="true"
            app:liftOnScroll="true">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/collapsing_layout"
                style="?collapsingToolbarLayoutLargeStyle"
                android:layout_width="match_parent"
                android:layout_height="?collapsingToolbarLayoutLargeSize"
                app:contentScrim="@android:color/transparent"
                app:layout_scrollFlags="scroll|snap|exitUntilCollapsed"
                app:statusBarScrim="@android:color/transparent">

                <com.google.android.material.appbar.MaterialToolbar
                    android:id="@+id/app_bar"
                    android:layout_width="match_parent"
                    android:layout_height="?actionBarSize"
                    android:elevation="0dp"
                    app:layout_collapseMode="pin">

                </com.google.android.material.appbar.MaterialToolbar>
            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <com.google.android.material.progressindicator.CircularProgressIndicator
            android:id="@+id/progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="16dp"
            android:indeterminate="true" />

        <LinearLayout
            android:id="@+id/empty_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:orientation="vertical"
            android:visibility="gone">

            <ImageView
                android:layout_width="128dp"
                android:layout_height="128dp"
                android:layout_gravity="center"
                android:contentDescription="@string/files_empty"
                android:src="@drawable/ic_file_empty" />

            <TextView
                style="@style/TextAppearance.Material3.BodyLarge"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@string/files_empty" />

        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:fitsSystemWindows="false"
            android:orientation="vertical"
            android:scrollbars="vertical"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_behavior="@string/appbar_scrolling_view_behavior"
            tools:listitem="@layout/translate_item" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/add_button"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_gravity="bottom"
            android:layout_margin="16dp"
            android:text="@string/add_files" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>
