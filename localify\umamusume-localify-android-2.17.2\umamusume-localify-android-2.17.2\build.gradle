apply plugin: 'idea'

idea.module {
    excludeDirs += file('out')
    resourceDirs += file('template')
    resourceDirs += file('scripts')
}

buildscript {
    repositories {
        mavenCentral()
        google()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.0.1'
    }
}

allprojects {
    repositories {
        mavenCentral()
        google()
    }
}

ext {
    minSdkVersion = 24
    targetSdkVersion = 33

    outDir = file("$rootDir/out")
}

tasks.register('clean') {
    delete rootProject.buildDir, outDir
}
