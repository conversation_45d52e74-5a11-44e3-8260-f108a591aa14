<?xml version="1.0" encoding="utf-8"?>
<resources>

    <declare-styleable name="FloatSeekBarPreference">
        <attr name="minFloat" format="float" />
        <attr name="max" format="float" />
        <attr name="android:layout" />
        <!--Attribute controlling the amount to increment or decrement the seekbar when the user
        presses the arrow keys-->
        <attr name="seekBarIncrementFloat" format="float" />
        <!-- Attribute indicating whether the slider within this preference can be adjusted, that is
        pressing left/right keys when this preference is focused will move the slider accordingly
        (e.g. inline adjustable preferences). False, if the slider within the preference is
        read-only and cannot be adjusted. By default, the seekbar is adjustable. -->
        <attr name="adjustable" format="boolean" />
        <!-- Flag indicating whether the TextView next to the seekbar that shows the current seekbar
        value will be displayed. If true, the view is VISIBLE; if false, the view will be GONE.
        By default, this view is GONE. -->
        <attr name="showSeekBarValue" format="boolean" />
        <!-- Flag indicating whether the SeekBarPreference should continuously save the Seekbar
        value while the Seekbar is being dragged. If true, the SeekBarPreference should continuously
        save the Seekbar value while it is being dragged. If false, the Seekbar value is only saved
        when released. By default, this boolean is false. -->
        <attr name="updatesContinuously" format="boolean" />
    </declare-styleable>


    <declare-styleable name="FilePickerPreference"><!-- The optional icon for the preference -->
        <attr name="icon"/>
        <attr name="android:icon"/>
        <!-- The key to store the Preference value. -->
        <attr format="string" name="key"/>
        <attr name="android:key"/>
        <!-- The title for the Preference in a PreferenceActivity screen. -->
        <attr name="title"/>
        <attr name="android:title"/>
        <!-- The summary for the Preference in a PreferenceActivity screen. -->
        <attr format="string" name="summary"/>
        <attr name="android:summary"/>
        <!-- The order for the Preference (lower values are to be ordered first). If this is not
             specified, the default ordering will be alphabetic. -->
        <attr format="integer" name="order"/>
        <attr name="android:order"/>
        <!-- When used inside of a modern PreferenceActivity, this declares
             a new PreferenceFragment to be shown when the user selects this item. -->
        <attr format="string" name="fragment"/>
        <attr name="android:fragment"/>
        <!-- The layout for the Preference in a PreferenceActivity screen. This should
             rarely need to be changed, look at widgetLayout instead. -->
        <attr name="layout"/>
        <attr name="android:layout"/>
        <!-- The layout for the controllable widget portion of a Preference. This is inflated
             into the layout for a Preference and should be used more frequently than
             the layout attribute. For example, a checkbox preference would specify
             a custom layout (consisting of just the CheckBox) here. -->
        <attr format="reference" name="widgetLayout"/>
        <attr name="android:widgetLayout"/>
        <!-- Whether the Preference is enabled. -->
        <attr format="boolean" name="enabled"/>
        <attr name="android:enabled"/>
        <!-- Whether the Preference is selectable. -->
        <attr format="boolean" name="selectable"/>
        <attr name="android:selectable"/>
        <!-- The key of another Preference that this Preference will depend on.  If the other
             Preference is not set or is off, this Preference will be disabled. -->
        <attr format="string" name="dependency"/>
        <attr name="android:dependency"/>
        <!-- Whether the Preference stores its value to the shared preferences. -->
        <attr format="boolean" name="persistent"/>
        <attr name="android:persistent"/>
        <!-- The default value for the preference, which will be set either if persistence
             is off or persistence is on and the preference is not found in the persistent
             storage.  -->
        <attr format="string|boolean|integer|reference|float" name="defaultValue"/>
        <attr name="android:defaultValue"/>
        <!-- Whether the view of this Preference should be disabled when
             this Preference is disabled. -->
        <attr format="boolean" name="shouldDisableView"/>
        <attr name="android:shouldDisableView"/>

        <!-- Whether the preference allows displaying divider on top -->
        <attr format="boolean" name="allowDividerAbove"/>

        <!-- Whether the preference allows displaying divider below it -->
        <attr format="boolean" name="allowDividerBelow"/>

        <!-- Whether to use single line for the preference title text. By default, preference title
             will be constrained to one line, so the default value of this attribute is true. -->
        <attr format="boolean" name="singleLineTitle"/>
        <attr name="android:singleLineTitle"/>

        <!-- Whether the space for the preference icon view will be reserved. If set to true, the
             preference will be offset as if it would have the icon and thus aligned with other
             preferences having icons. By default, preference icon view visibility will be set to
             GONE when there is no icon provided, so the default value of this attribute is false.
             -->
        <attr format="boolean" name="iconSpaceReserved"/>
        <attr name="android:iconSpaceReserved"/>

        <!-- Whether the Preference is visible. By default, this is set to true. -->
        <attr format="boolean" name="isPreferenceVisible"/>

        <!-- Whether the summary of this preference can be copied to the clipboard by long pressing
             on the preference. By default, this is set to false. -->
        <attr format="boolean" name="enableCopying"/>
        <attr name="android:mimeType" format="string" />
    </declare-styleable>
</resources>
