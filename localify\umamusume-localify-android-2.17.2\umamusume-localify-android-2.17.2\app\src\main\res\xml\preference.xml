<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <Preference
        android:icon="@drawable/ic_carrot"
        android:key="version"
        android:summary="@string/unknown"
        android:title="@string/pref_module_version" />

    <PreferenceCategory android:title="@string/pref_cate_logger">

        <SwitchPreference
            android:defaultValue="false"
            android:icon="@drawable/ic_logger"
            android:key="enableLogger"
            android:title="@string/pref_enable_logger" />
        <CheckBoxPreference
            android:defaultValue="false"
            android:key="dumpStaticEntries"
            android:title="@string/pref_dump_static_entries" />
        <CheckBoxPreference
            android:defaultValue="false"
            android:key="dumpDbEntries"
            android:title="@string/pref_dump_db_entries" />

        <Preference
            android:fragment="com.kimjio.umamusumelocalify.settings.fragment.MsgPackSettingsFragment"
            android:icon="@drawable/ic_dump_msgpack"
            android:key="msgpackDump"
            android:title="@string/pref_dump_msgpack" />
    </PreferenceCategory>

    <PreferenceCategory android:title="@string/pref_cate_graphics">

        <SeekBarPreference
            android:defaultValue="30"
            android:icon="@drawable/ic_fps"
            android:key="maxFps"
            android:max="144"
            android:title="@string/pref_max_fps"
            app:min="30"
            app:showSeekBarValue="true"
            tools:defaultValue="90" />

        <com.kimjio.umamusumelocalify.settings.preference.FloatSeekBarPreference
            android:defaultValue="1.0"
            android:icon="@drawable/ic_animation"
            android:key="uiAnimationScale"
            android:title="@string/pref_ui_animation_scale"
            app:max="10.0"
            app:minFloat="0.1"
            app:seekBarIncrementFloat="0.1"
            app:showSeekBarValue="true"
            tools:defaultValue="1.0" />

        <com.kimjio.umamusumelocalify.settings.preference.FloatSeekBarPreference
            android:defaultValue="1.0"
            android:icon="@drawable/ic_resolution_3d_scale"
            android:key="resolution3dScale"
            android:title="@string/pref_3d_resolution_scale"
            app:max="3.0"
            app:minFloat="0.1"
            app:seekBarIncrementFloat="0.1"
            app:showSeekBarValue="true"
            tools:defaultValue="1.0" />

        <com.kimjio.umamusumelocalify.settings.preference.IntegerDropDownPreference
            android:defaultValue="-1"
            android:icon="@drawable/ic_graphics_quality"
            android:key="graphicsQuality"
            app:entries="@array/graphics_quality"
            app:entryValues="@array/graphics_quality_value"
            app:title="@string/pref_graphics_quality"
            app:useSimpleSummaryProvider="true" />

        <com.kimjio.umamusumelocalify.settings.preference.IntegerDropDownPreference
            android:defaultValue="-1"
            android:key="antiAliasing"
            app:entries="@array/anti_aliasing"
            app:entryValues="@array/anti_aliasing_value"
            app:title="@string/pref_anti_aliasing"
            app:useSimpleSummaryProvider="true" />

        <com.kimjio.umamusumelocalify.settings.preference.IntegerDropDownPreference
            android:defaultValue="@null"
            android:icon="@drawable/ic_update"
            android:key="cySpringUpdateMode"
            app:entries="@array/cyspring_update_mode"
            app:entryValues="@array/cyspring_update_mode_value"
            app:title="@string/pref_cyspring_update_mode"
            app:useSimpleSummaryProvider="true" />

    </PreferenceCategory>

    <PreferenceCategory android:title="@string/pref_cate_screen">

        <SwitchPreference
            android:defaultValue="false"
            android:icon="@drawable/ic_system_resolution"
            android:key="uiUseSystemResolution"
            android:title="@string/pref_use_system_resolution" />

        <SwitchPreference
            android:defaultValue="false"
            android:icon="@drawable/ic_builtin_font"
            android:key="replaceToBuiltinFont"
            android:title="@string/pref_replace_builtin_font" />

        <SwitchPreference
            android:defaultValue="false"
            android:icon="@drawable/ic_custom_font"
            android:key="replaceToCustomFont"
            android:title="@string/pref_replace_custom_font" />

        <com.kimjio.umamusumelocalify.settings.preference.FilePickerPreference
            android:key="fontAssetBundlePath"
            android:title="@string/pref_font_asset_path" />

        <EditTextPreference
            android:key="fontAssetName"
            android:title="@string/pref_font_asset_name"
            app:useSimpleSummaryProvider="true" />

        <EditTextPreference
            android:key="tmproFontAssetName"
            android:title="@string/pref_tmpro_font_asset_name"
            app:useSimpleSummaryProvider="true" />

        <SwitchPreference
            android:defaultValue="false"
            android:icon="@drawable/ic_landscape"
            android:key="forceLandscape"
            android:title="@string/pref_force_landscape" />

        <com.kimjio.umamusumelocalify.settings.preference.FloatSeekBarPreference
            android:defaultValue="1.0"
            android:icon="@drawable/ic_ui_scale"
            android:key="forceLandscapeUiScale"
            android:title="@string/pref_force_landscape_ui_scale"
            app:max="2.0"
            app:minFloat="0.1"
            app:seekBarIncrementFloat="0.1"
            app:showSeekBarValue="true"
            tools:defaultValue="1.0" />

        <SwitchPreference
            android:defaultValue="true"
            android:icon="@drawable/ic_orientation"
            android:key="uiLoadingShowOrientationGuide"
            android:title="@string/pref_loading_show_orientation_guide" />

        <SwitchPreference
            android:defaultValue="false"
            android:key="hideNowLoading"
            android:title="@string/pref_hide_loading_screen" />

        <SwitchPreference
            android:defaultValue="false"
            android:icon="@drawable/ic_caption"
            android:key="characterSystemTextCaption"
            android:title="@string/pref_character_system_text_caption" />
    </PreferenceCategory>

    <PreferenceCategory android:title="@string/pref_cate_etc">

        <!--<SwitchPreference
            app:isPreferenceVisible="false"
            android:defaultValue="true"
            app:icon="@drawable/ic_notification"
            android:key="restoreNotification"
            android:title="@string/pref_restore_notification" />-->

        <SwitchPreference
            android:defaultValue="true"
            android:key="restoreGallopWebview"
            android:summary="@string/pref_restore_gallop_webview_summary"
            android:title="@string/pref_restore_gallop_webview"
            app:icon="@drawable/ic_web"
            app:isPreferenceVisible="false"
            tools:isPreferenceVisible="true" />

        <SwitchPreference
            android:defaultValue="false"
            android:key="useThirdPartyNews"
            android:summary="@string/pref_use_third_party_news_summary"
            android:title="@string/pref_use_third_party_news"
            app:isPreferenceVisible="false"
            tools:isPreferenceVisible="true" />

        <com.kimjio.umamusumelocalify.settings.preference.FolderPickerPreference
            android:icon="@drawable/ic_replace_assets_folder"
            android:key="replaceAssetsPath"
            android:title="@string/pref_replace_assets_path" />

        <com.kimjio.umamusumelocalify.settings.preference.FilePickerPreference
            android:icon="@drawable/ic_replace_asset_bundle"
            android:key="replaceAssetBundleFilePath"
            android:title="@string/pref_replace_asset_bundle" />

        <!--<com.kimjio.umamusumelocalify.settings.preference.FilePickerPreference
            android:icon="@drawable/ic_database"
            android:key="replaceTextDBPath"
            android:title="Database"
            android:mimeType="application/msaccess"/>-->

        <CheckBoxPreference
            android:defaultValue="false"
            android:icon="@drawable/ic_static_hash"
            android:key="staticEntriesUseHash"
            android:title="@string/pref_static_entries_use_hash" />

        <CheckBoxPreference
            android:defaultValue="false"
            android:icon="@drawable/ic_static_text_id"
            android:key="staticEntriesUseTextIdName"
            android:title="@string/pref_static_entries_use_text_id" />

        <com.kimjio.umamusumelocalify.settings.preference.FilePickerPreference
            android:icon="@drawable/ic_json"
            android:key="textIdDict"
            android:mimeType="application/json"
            android:title="@string/pref_text_id_file" />

        <Preference
            android:icon="@drawable/ic_translate"
            android:key="manageTranslate"
            android:title="@string/manage_translate" />
    </PreferenceCategory>

</PreferenceScreen>
