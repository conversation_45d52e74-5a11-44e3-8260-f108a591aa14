<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".activity.SettingsActivity">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fitsSystemWindows="true"
            app:liftOnScroll="true">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/collapsing_layout"
                style="?collapsingToolbarLayoutLargeStyle"
                android:layout_width="match_parent"
                android:layout_height="?collapsingToolbarLayoutLargeSize"
                app:contentScrim="@android:color/transparent"
                app:layout_scrollFlags="scroll|snap|exitUntilCollapsed"
                app:statusBarScrim="@android:color/transparent">

                <com.google.android.material.appbar.MaterialToolbar
                    android:id="@+id/app_bar"
                    android:layout_width="match_parent"
                    android:layout_height="?actionBarSize"
                    android:elevation="0dp"
                    app:layout_collapseMode="pin">

                </com.google.android.material.appbar.MaterialToolbar>
            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <FrameLayout
            android:id="@+id/frame"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>
